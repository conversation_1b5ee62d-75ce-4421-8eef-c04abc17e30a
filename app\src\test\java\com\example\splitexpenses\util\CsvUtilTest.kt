package com.example.splitexpenses.util

import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.Date

class CsvUtilTest {

    private lateinit var testGroup: GroupData
    private lateinit var testExpense: Expense

    @Before
    fun setup() {
        // Create test data
        testExpense = Expense(
            id = "test-expense-id",
            amount = 50.0,
            description = "Test Expense",
            paidBy = "Alice",
            splitBetween = listOf("Alice", "Bob"),
            category = "Food",
            date = Date().time,
            timestamp = Date().time,
            isCategoryLocked = true
        )

        val testCategories = listOf(
            Category("Food", "🍔", listOf("food", "restaurant")),
            Category("Transport", "🚗", listOf("car", "bus"))
        )

        val testMemberAvatars = mapOf(
            "Alice" to "😊",
            "<PERSON>" to "😎"
        )

        testGroup = GroupData(
            id = "test-group-id",
            name = "Test Group",
            members = listOf("Alice", "Bob"),
            expenses = listOf(testExpense),
            memberUidMap = mapOf("Alice" to "device1"),
            allowedUsers = listOf("device1"),
            creatorUid = "device1",
            categories = testCategories,
            memberAvatars = testMemberAvatars
        )
    }

    @Test
    fun `test export and import round trip`() {
        // Export the group to CSV
        val outputStream = ByteArrayOutputStream()
        val exportResult = CsvUtil.exportGroupToCsv(testGroup, outputStream)
        assertTrue("Export should succeed", exportResult)

        // Get the CSV content
        val csvContent = outputStream.toString()
        println("Exported CSV content:\n$csvContent")

        // Import the CSV content back
        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Verify the import was successful
        assertTrue("Import should succeed", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", testGroup.name, importedGroup.name)
        assertEquals("Members should match", testGroup.members, importedGroup.members)
        
        // Verify categories were imported correctly
        assertEquals("Number of categories should match", testGroup.categories.size, importedGroup.categories.size)
        assertEquals("Category names should match", 
            testGroup.categories.map { it.name }, 
            importedGroup.categories.map { it.name }
        )
        assertEquals("Category emojis should match", 
            testGroup.categories.map { it.emoji }, 
            importedGroup.categories.map { it.emoji }
        )
        
        // Verify member avatars were imported correctly
        assertEquals("Member avatars should match", testGroup.memberAvatars, importedGroup.memberAvatars)
        
        // Verify expenses were imported correctly
        assertEquals("Number of expenses should match", testGroup.expenses.size, importedGroup.expenses.size)
        val importedExpense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", testExpense.amount, importedExpense.amount, 0.001)
        assertEquals("Expense description should match", testExpense.description, importedExpense.description)
        assertEquals("Expense paidBy should match", testExpense.paidBy, importedExpense.paidBy)
        assertEquals("Expense splitBetween should match", testExpense.splitBetween, importedExpense.splitBetween)
        assertEquals("Expense category should match", testExpense.category, importedExpense.category)
        assertEquals("Expense isCategoryLocked should match", testExpense.isCategoryLocked, importedExpense.isCategoryLocked)
    }
}
