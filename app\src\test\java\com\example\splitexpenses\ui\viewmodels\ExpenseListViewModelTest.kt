package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.ExpenseRepository
import com.example.splitexpenses.data.repositories.GroupRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayOutputStream

@ExperimentalCoroutinesApi
class ExpenseListViewModelTest {

    private lateinit var expenseRepository: ExpenseRepository
    private lateinit var groupRepository: GroupRepository
    private lateinit var viewModel: ExpenseListViewModel

    private val testDispatcher = StandardTestDispatcher()

    private val testGroupId = "test-group-id"
    private val testGroup = GroupData(
        id = testGroupId,
        name = "Test Group",
        members = listOf("Alice", "Bob", "Charlie"),
        expenses = listOf(
            Expense(
                id = "expense1",
                amount = 100.0,
                description = "Dinner",
                paidBy = "Alice",
                splitBetween = listOf("Alice", "Bob", "Charlie"),
                category = "Food",
                date = 1625097600000 // 2021-07-01
            ),
            Expense(
                id = "expense2",
                amount = 60.0,
                description = "Taxi",
                paidBy = "Bob",
                splitBetween = listOf("Alice", "Bob"),
                category = "Transport",
                date = 1625184000000 // 2021-07-02
            )
        ),
        currentUser = "Alice",
        allowedUsers = listOf("device1", "device2", "device3")
    )

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        expenseRepository = mockk(relaxed = true)
        groupRepository = mockk(relaxed = true)

        // Mock the currentGroup flow in the expenseRepository
        val currentGroupFlow = MutableStateFlow(testGroup)
        every { expenseRepository.currentGroup } returns currentGroupFlow

        viewModel = ExpenseListViewModel(expenseRepository, groupRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state should have empty selected expenses and multi-select mode disabled`() {
        // Then
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedExpenses)
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertFalse(viewModel.uiState.value.isLoading)
        assertEquals(null, viewModel.uiState.value.error)
    }

    @Test
    fun `addExpense should call repository with correct parameters`() = runTest {
        // Given
        val amount = 50.0
        val description = "Groceries"
        val paidBy = "Charlie"
        val splitBetween = listOf("Alice", "Charlie")
        val category = "Groceries"
        val date = 1625270400000L // 2021-07-03

        // When
        viewModel.addExpense(amount, description, paidBy, splitBetween, category, date)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify {
            expenseRepository.addExpense(
                amount = amount,
                description = description,
                paidBy = paidBy,
                splitBetween = splitBetween,
                category = category,
                date = date
            )
        }
    }

    @Test
    fun `updateExpense should call repository with correct parameters`() = runTest {
        // Given
        val expenseId = "expense1"
        val amount = 120.0
        val description = "Fancy Dinner"
        val paidBy = "Alice"
        val splitBetween = listOf("Alice", "Bob", "Charlie")
        val category = "Food"
        val date = 1625097600000L // 2021-07-01

        // When
        viewModel.updateExpense(expenseId, amount, description, paidBy, splitBetween, category, date)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify {
            expenseRepository.updateExpense(
                expenseId = expenseId,
                amount = amount,
                description = description,
                paidBy = paidBy,
                splitBetween = splitBetween,
                category = category,
                date = date
            )
        }
    }

    @Test
    fun `deleteExpense should call repository with correct parameters`() = runTest {
        // Given
        val expenseId = "expense1"

        // When
        viewModel.deleteExpense(expenseId)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { expenseRepository.deleteExpense(expenseId) }
    }

    @Test
    fun `deleteExpenses should call repository and update UI state`() = runTest {
        // Given
        val expenseIds = setOf("expense1", "expense2")

        // Set initial state with selected expenses and multi-select mode enabled
        viewModel.setMultiSelectMode(true)
        viewModel.toggleExpenseSelection("expense1", true)
        viewModel.toggleExpenseSelection("expense2", true)

        // When
        viewModel.deleteExpenses(expenseIds)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { expenseRepository.deleteExpenses(expenseIds) }

        // Verify UI state is updated
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedExpenses)
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
    }

    @Test
    fun `setMultiSelectMode should update UI state`() {
        // When
        viewModel.setMultiSelectMode(true)

        // Then
        assertTrue(viewModel.uiState.value.isMultiSelectMode)

        // When
        viewModel.setMultiSelectMode(false)

        // Then
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedExpenses)
    }

    @Test
    fun `toggleExpenseSelection should update selected expenses`() {
        // Given
        val expenseId = "expense1"

        // When - Select expense
        viewModel.toggleExpenseSelection(expenseId, true)

        // Then
        assertTrue(expenseId in viewModel.uiState.value.selectedExpenses)

        // When - Deselect expense
        viewModel.toggleExpenseSelection(expenseId, false)

        // Then
        assertFalse(expenseId in viewModel.uiState.value.selectedExpenses)
    }

    @Test
    fun `toggleExpenseSelection should disable multi-select mode when all items are deselected`() {
        // Given - Enable multi-select mode and select an expense
        viewModel.setMultiSelectMode(true)
        val expenseId = "expense1"
        viewModel.toggleExpenseSelection(expenseId, true)

        // Verify initial state
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(expenseId in viewModel.uiState.value.selectedExpenses)

        // When - Deselect the last selected expense
        viewModel.toggleExpenseSelection(expenseId, false)

        // Then - Multi-select mode should be automatically disabled
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(viewModel.uiState.value.selectedExpenses.isEmpty())
    }

    @Test
    fun `toggleExpenseSelection should keep multi-select mode when items remain selected`() {
        // Given - Enable multi-select mode and select multiple expenses
        viewModel.setMultiSelectMode(true)
        val expenseId1 = "expense1"
        val expenseId2 = "expense2"
        viewModel.toggleExpenseSelection(expenseId1, true)
        viewModel.toggleExpenseSelection(expenseId2, true)

        // Verify initial state
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertEquals(2, viewModel.uiState.value.selectedExpenses.size)

        // When - Deselect one expense (but not all)
        viewModel.toggleExpenseSelection(expenseId1, false)

        // Then - Multi-select mode should remain enabled
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(expenseId2 in viewModel.uiState.value.selectedExpenses)
        assertFalse(expenseId1 in viewModel.uiState.value.selectedExpenses)
    }

    @Test
    fun `exportToCsv should call repository with correct parameters`() {
        // Given
        val outputStream = ByteArrayOutputStream()

        // Mock the exportGroupToCsv method
        every { groupRepository.exportGroupToCsv(any()) } returns true

        // When
        val result = viewModel.exportToCsv(outputStream)

        // Then
        verify { groupRepository.exportGroupToCsv(outputStream) }
        assertTrue(result)
    }

    @Test
    fun `deleteCurrentGroup should call repository with correct parameters`() = runTest {
        // When
        viewModel.deleteCurrentGroup()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { groupRepository.deleteGroup(testGroupId) }
    }

    @Test
    fun `updateGroupMembers should call repository with correct parameters`() = runTest {
        // Given
        val newMembers = listOf("Alice", "Bob", "Charlie", "David")

        // When
        viewModel.updateGroupMembers(newMembers)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { groupRepository.updateGroupMembers(testGroupId, newMembers) }
    }
}
