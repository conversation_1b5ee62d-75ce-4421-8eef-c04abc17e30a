package com.example.splitexpenses.data

import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import com.example.splitexpenses.data.repositories.OfflineCapableRepository
import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.OfflineDataSource
import com.example.splitexpenses.data.sync.SyncQueueManager
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import java.util.UUID

class OfflineCapableRepositoryTest {

    private lateinit var remoteDataSource: DataSource
    private lateinit var offlineDataSource: OfflineDataSource
    private lateinit var networkConnectivityManager: NetworkConnectivityManager
    private lateinit var syncQueueManager: SyncQueueManager
    private lateinit var repository: OfflineCapableRepository

    @Before
    fun setup() {
        remoteDataSource = mockk()
        offlineDataSource = mockk()
        networkConnectivityManager = mockk()
        syncQueueManager = mockk()

        repository = OfflineCapableRepository(
            remoteDataSource,
            offlineDataSource,
            networkConnectivityManager,
            syncQueueManager
        )
    }

    @Test
    fun `when online, saveGroup should save to remote and cache locally`() = runTest {
        // Given
        val group = createTestGroup()
        every { networkConnectivityManager.isConnected() } returns flowOf(true)
        coEvery { remoteDataSource.saveGroup(group) } returns Unit
        coEvery { offlineDataSource.cacheGroup(group) } returns Unit

        // When
        repository.saveGroup(group)

        // Then
        coVerify { remoteDataSource.saveGroup(group) }
        coVerify { offlineDataSource.cacheGroup(group) }
    }

    @Test
    fun `when offline, saveGroup should save locally and queue for sync`() = runTest {
        // Given
        val group = createTestGroup()
        every { networkConnectivityManager.isConnected() } returns flowOf(false)
        coEvery { offlineDataSource.saveGroup(group) } returns Unit
        coEvery { syncQueueManager.queueGroupOperation(any(), any()) } returns Unit

        // When
        repository.saveGroup(group)

        // Then
        coVerify { offlineDataSource.saveGroup(group) }
        coVerify { syncQueueManager.queueGroupOperation(group, any()) }
        coVerify(exactly = 0) { remoteDataSource.saveGroup(group) }
    }

    @Test
    fun `when online, addExpense should add to remote and cache locally`() = runTest {
        // Given
        val groupId = "test-group"
        val expense = createTestExpense()
        every { networkConnectivityManager.isConnected() } returns flowOf(true)
        coEvery { remoteDataSource.addExpense(groupId, expense) } returns Unit
        coEvery { offlineDataSource.addExpense(groupId, expense) } returns Unit

        // When
        repository.addExpense(groupId, expense)

        // Then
        coVerify { remoteDataSource.addExpense(groupId, expense) }
        coVerify { offlineDataSource.addExpense(groupId, expense) }
    }

    @Test
    fun `when offline, addExpense should save locally and queue for sync`() = runTest {
        // Given
        val groupId = "test-group"
        val expense = createTestExpense()
        every { networkConnectivityManager.isConnected() } returns flowOf(false)
        coEvery { offlineDataSource.addExpense(groupId, expense) } returns Unit
        coEvery { syncQueueManager.queueExpenseOperation(any(), any(), any()) } returns Unit

        // When
        repository.addExpense(groupId, expense)

        // Then
        coVerify { offlineDataSource.addExpense(groupId, expense) }
        coVerify { syncQueueManager.queueExpenseOperation(expense, groupId, any()) }
        coVerify(exactly = 0) { remoteDataSource.addExpense(groupId, expense) }
    }

    @Test
    fun `when offline, updateExpense should throw exception`() = runTest {
        // Given
        val groupId = "test-group"
        val expense = createTestExpense()
        every { networkConnectivityManager.isConnected() } returns flowOf(false)

        // When & Then
        try {
            repository.updateExpense(groupId, expense)
            assert(false) { "Expected IllegalStateException" }
        } catch (e: IllegalStateException) {
            assert(e.message?.contains("Cannot edit existing expenses while offline") == true)
        }
    }

    @Test
    fun `when offline, deleteExpense should throw exception`() = runTest {
        // Given
        val groupId = "test-group"
        val expenseId = "test-expense"
        every { networkConnectivityManager.isConnected() } returns flowOf(false)

        // When & Then
        try {
            repository.deleteExpense(groupId, expenseId)
            assert(false) { "Expected IllegalStateException" }
        } catch (e: IllegalStateException) {
            assert(e.message?.contains("Cannot delete expenses while offline") == true)
        }
    }

    private fun createTestGroup(): GroupData {
        return GroupData(
            id = UUID.randomUUID().toString(),
            name = "Test Group",
            members = listOf("Alice", "Bob"),
            expenses = emptyList(),
            allowedUsers = listOf("user1", "user2"),
            creatorUid = "user1"
        )
    }

    private fun createTestExpense(): Expense {
        return Expense(
            id = UUID.randomUUID().toString(),
            amount = 25.50,
            description = "Test Expense",
            paidBy = "Alice",
            splitBetween = listOf("Alice", "Bob"),
            category = "Food",
            date = System.currentTimeMillis()
        )
    }
}
