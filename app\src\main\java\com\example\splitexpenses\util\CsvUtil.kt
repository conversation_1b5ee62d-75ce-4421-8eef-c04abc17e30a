package com.example.splitexpenses.util

import java.io.InputStream
import java.io.OutputStream
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import kotlin.math.absoluteValue

import android.util.Log

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.getDefaultCategories

/**
 * Utility class for handling CSV export and import operations
 */
object CsvUtil {
    // Tag for logging
    private const val TAG = "CsvUtil"

    // CSV format constants
    private const val CSV_DELIMITER = ","
    private const val CSV_QUOTE = "\""
    private const val CSV_ESCAPE = "\\"
    private const val CSV_NEWLINE = "\n"

    // Date format for CSV export/import
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // CSV Headers - excluding id and timestamp fields
    private val GROUP_HEADER = listOf("name", "members", "memberAvatars", "categories")
    private val EXPENSE_HEADER = listOf(
        "amount", "description", "paidBy",
        "splitBetween", "category", "date", "isCategoryLocked"
    )

    /**
     * Escapes a string for CSV format
     * @param value The string to escape
     * @return The escaped string
     */
    private fun escapeForCsv(value: String): String {
        // If the value contains a delimiter, quote, or newline, wrap it in quotes and escape quotes
        return if (value.contains(CSV_DELIMITER) || value.contains(CSV_QUOTE) || value.contains(CSV_NEWLINE)) {
            CSV_QUOTE + value.replace(CSV_QUOTE, CSV_ESCAPE + CSV_QUOTE) + CSV_QUOTE
        } else {
            value
        }
    }

    /**
     * Trims trailing empty fields from a list of CSV fields
     * @param fields The list of fields to trim
     * @param expectedSize The expected number of fields
     * @return The trimmed list of fields and a boolean indicating if trimming occurred
     */
    private fun trimTrailingEmptyFields(fields: List<String>, expectedSize: Int): Pair<List<String>, Boolean> {
        // If the list is already the expected size or smaller, return it as is
        if (fields.size <= expectedSize) {
            return Pair(fields, false)
        }

        // Find the last non-empty field index
        var lastNonEmptyIndex = fields.size - 1
        while (lastNonEmptyIndex >= expectedSize && fields[lastNonEmptyIndex].isBlank()) {
            lastNonEmptyIndex--
        }

        // If we found trailing empty fields, trim them
        val trimmedFields = if (lastNonEmptyIndex < fields.size - 1) {
            // Keep all fields up to the last non-empty one, plus one more if needed to meet expectedSize
            val endIndex = maxOf(lastNonEmptyIndex + 1, expectedSize)
            fields.subList(0, endIndex)
        } else {
            // No trailing empty fields to trim
            fields
        }

        // Return the trimmed fields and whether trimming occurred
        return Pair(trimmedFields, trimmedFields.size != fields.size)
    }

    /**
     * Converts a list to a CSV-friendly string
     * @param list The list to convert
     * @return A string representation of the list
     */
    private fun listToCsvString(list: List<String>): String {
        return escapeForCsv(list.joinToString("|"))
    }

    /**
     * Converts a CSV string back to a list
     * @param csvString The CSV string to convert
     * @return A list of strings
     */
    private fun csvStringToList(csvString: String): List<String> {
        return if (csvString.isBlank()) {
            emptyList()
        } else {
            csvString.split("|")
        }
    }

    /**
     * Exports a group to CSV format
     * @param group The group to export
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    fun exportGroupToCsv(group: GroupData, outputStream: OutputStream): Boolean {
        return try {
            // Write group header
            val groupHeaderLine = GROUP_HEADER.joinToString(CSV_DELIMITER)
            outputStream.write("$groupHeaderLine$CSV_NEWLINE".toByteArray())

            // Convert memberAvatars map to a string
            val memberAvatarsString = group.memberAvatars.entries.joinToString(";") { "${it.key}=${it.value}" }

            // Convert categories to a string
            val categoriesString = group.categories.joinToString(";") {
                "${it.name}~${it.emoji}~${it.keywords.joinToString("^")}"
            }

            // Write group data (excluding id, memberUidMap, allowedUsers, and creatorUid)
            val groupDataLine = listOf(
                escapeForCsv(group.name),
                escapeForCsv(listToCsvString(group.members)),
                escapeForCsv(memberAvatarsString),
                escapeForCsv(categoriesString)
            ).joinToString(CSV_DELIMITER)
            outputStream.write("$groupDataLine$CSV_NEWLINE".toByteArray())

            // Write expense header
            val expenseHeaderLine = EXPENSE_HEADER.joinToString(CSV_DELIMITER)
            outputStream.write("$expenseHeaderLine$CSV_NEWLINE".toByteArray())

            // Write expense data (excluding id and timestamp)
            group.expenses.forEach { expense ->
                val expenseLine = listOf(
                    expense.amount.toString(),
                    escapeForCsv(expense.description),
                    escapeForCsv(expense.paidBy),
                    escapeForCsv(listToCsvString(expense.splitBetween)),
                    escapeForCsv(expense.category),
                    dateFormat.format(Date(expense.date)),
                    expense.isCategoryLocked.toString()
                ).joinToString(CSV_DELIMITER)
                outputStream.write("$expenseLine$CSV_NEWLINE".toByteArray())
            }

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            try {
                outputStream.close()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * Imports a group from CSV format with detailed error handling and logging
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional, will use from CSV if not provided)
     * @return CsvImportResult containing the import results, errors, and warnings
     */
    fun importGroupFromCsv(inputStream: InputStream, currentUser: String? = null): CsvImportResult {
        Log.d(TAG, "Starting CSV import process")

        val errors = mutableListOf<CsvImportError>()
        val warnings = mutableListOf<CsvImportWarning>()
        var totalRowsProcessed = 0
        var successfulRows = 0

        try {
            // Read all lines from the input stream
            Log.d(TAG, "Reading lines from input stream")
            val lines = inputStream.bufferedReader().readLines()

            // Validate minimum required lines
            if (lines.size < 4) {
                val error = CsvImportError(
                    message = "CSV file must contain at least 4 lines (group header, group data, expense header, and at least one expense)",
                    lineNumber = lines.size
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Parse group header
            Log.d(TAG, "Parsing group header")
            val rawGroupHeader = try {
                lines[0].split(CSV_DELIMITER)
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse group header",
                    lineNumber = 1,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Trim trailing empty fields from group header
            val (groupHeader, headerTrimmed) = trimTrailingEmptyFields(rawGroupHeader, GROUP_HEADER.size)

            // Just log the trimming operation without adding a warning
            if (headerTrimmed) {
                Log.d(TAG, "Trailing empty fields detected in group header and removed: " +
                        "From: ${rawGroupHeader.joinToString(",")} To: ${groupHeader.joinToString(",")}")
            }

            // Validate group header
            if (groupHeader.size != GROUP_HEADER.size || !groupHeader.zip(GROUP_HEADER).all { (a, b) -> a == b }) {
                val error = CsvImportError(
                    message = "Invalid group header. Expected: ${GROUP_HEADER.joinToString(",")}. Found: ${groupHeader.joinToString(",")}",
                    lineNumber = 1
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Parse group data
            Log.d(TAG, "Parsing group data")
            val rawGroupData = try {
                lines[1].split(CSV_DELIMITER)
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse group data",
                    lineNumber = 2,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Trim trailing empty fields from group data
            val (groupData, dataTrimmed) = trimTrailingEmptyFields(rawGroupData, GROUP_HEADER.size)

            // Just log the trimming operation without adding a warning
            if (dataTrimmed) {
                Log.d(TAG, "Trailing empty fields detected in group data and removed: " +
                        "From: ${rawGroupData.joinToString(",")} To: ${groupData.joinToString(",")}")
            }

            // Validate group data
            if (groupData.size < GROUP_HEADER.size) {
                val error = CsvImportError(
                    message = "Group data has fewer fields than expected. Expected: ${GROUP_HEADER.size}, Found: ${groupData.size}",
                    lineNumber = 2
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Extract group fields
            val groupId = UUID.randomUUID().toString() // Generate new ID for imported group

            // Extract and validate group name
            val groupName = if (groupData[0].isNotBlank()) {
                groupData[0]
            } else {
                val warning = CsvImportWarning(
                    message = "Group name is empty, using 'Imported Group'",
                    lineNumber = 2,
                    fieldName = "name",
                    fieldValue = "",
                    fixedValue = "Imported Group"
                )
                warnings.add(warning)
                Log.w(TAG, warning.toString())
                "Imported Group"
            }

            // Extract and validate members list
            var members = try {
                val membersList = csvStringToList(groupData[1])
                if (membersList.isEmpty()) {
                    val warning = CsvImportWarning(
                        message = "Members list is empty",
                        lineNumber = 2,
                        fieldName = "members"
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString())
                }
                membersList
            } catch (e: Exception) {
                val warning = CsvImportWarning(
                    message = "Failed to parse members list, using empty list",
                    lineNumber = 2,
                    fieldName = "members",
                    fieldValue = groupData[1],
                    fixedValue = "[]"
                )
                warnings.add(warning)
                Log.w(TAG, warning.toString(), e)
                emptyList()
            }

            // Current user is now handled client-side, not in the CSV

            // Parse expense header
            Log.d(TAG, "Parsing expense header")
            val rawExpenseHeader = try {
                lines[2].split(CSV_DELIMITER)
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse expense header",
                    lineNumber = 3,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Trim trailing empty fields from expense header
            val (expenseHeader, expenseHeaderTrimmed) = trimTrailingEmptyFields(rawExpenseHeader, EXPENSE_HEADER.size)

            // Just log the trimming operation without adding a warning
            if (expenseHeaderTrimmed) {
                Log.d(TAG, "Trailing empty fields detected in expense header and removed: " +
                        "From: ${rawExpenseHeader.joinToString(",")} To: ${expenseHeader.joinToString(",")}")
            }

            // Validate expense header - the first 5 fields are required, date field is optional
            val requiredExpenseHeader = EXPENSE_HEADER.take(5)
            val headerToCheck = expenseHeader.take(minOf(expenseHeader.size, 5))

            if (headerToCheck.size < requiredExpenseHeader.size ||
                !headerToCheck.zip(requiredExpenseHeader).all { (a, b) -> a == b }) {
                val error = CsvImportError(
                    message = "Invalid expense header. Required: ${requiredExpenseHeader.joinToString(",")}. Found: ${headerToCheck.joinToString(",")}",
                    lineNumber = 3
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Check if date field is present but has a different name
            if (expenseHeader.size >= EXPENSE_HEADER.size && expenseHeader[5] != EXPENSE_HEADER[5]) {
                Log.d(TAG, "Date field has different name in header: '${expenseHeader[5]}' instead of '${EXPENSE_HEADER[5]}', will still try to parse it")
            }

            // Parse expense data
            Log.d(TAG, "Parsing expense data")
            val expenses = mutableListOf<Expense>()

            for (i in 3 until lines.size) {
                totalRowsProcessed++
                val lineNumber = i + 1 // 1-based line number for error messages

                // Skip empty lines
                if (lines[i].isBlank()) {
                    val warning = CsvImportWarning(
                        message = "Skipping empty line",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString())
                    continue
                }

                val rawExpenseData = try {
                    lines[i].split(CSV_DELIMITER)
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse expense data, skipping line: ${e.message}",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                    continue
                }

                // Trim trailing empty fields from expense data
                val (expenseData, expenseDataTrimmed) = trimTrailingEmptyFields(rawExpenseData, EXPENSE_HEADER.size)

                // Just log the trimming operation without adding a warning
                if (expenseDataTrimmed) {
                    Log.d(TAG, "Trailing empty fields detected in expense data at line $lineNumber and removed: " +
                            "From: ${rawExpenseData.joinToString(",")} To: ${expenseData.joinToString(",")}")
                }

                // Validate expense data has enough fields for the essential data (amount, description, paidBy, splitBetween, category)
                // Date field (index 5) is optional and will be handled separately
                if (expenseData.size < 5) {
                    val warning = CsvImportWarning(
                        message = "Expense data has fewer fields than required. Required: 5 (amount, description, paidBy, splitBetween, category), Found: ${expenseData.size}, skipping line",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString())
                    continue
                }

                try {
                    // Generate new ID for imported expense
                    val expenseId = UUID.randomUUID().toString()

                    // Parse amount
                    val amount = try {
                        val parsedAmount = expenseData[0].toDoubleOrNull()
                        if (parsedAmount == null) {
                            val warning = CsvImportWarning(
                                message = "Invalid amount format, using 0.0",
                                lineNumber = lineNumber,
                                fieldName = "amount",
                                fieldValue = expenseData[0],
                                fixedValue = "0.0"
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            0.0
                        } else if (parsedAmount < 0) {
                            val warning = CsvImportWarning(
                                message = "Negative amount, using absolute value",
                                lineNumber = lineNumber,
                                fieldName = "amount",
                                fieldValue = expenseData[0],
                                fixedValue = parsedAmount.absoluteValue.toString()
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            parsedAmount.absoluteValue
                        } else {
                            parsedAmount
                        }
                    } catch (e: Exception) {
                        val warning = CsvImportWarning(
                            message = "Failed to parse amount, using 0.0",
                            lineNumber = lineNumber,
                            fieldName = "amount",
                            fieldValue = expenseData[0],
                            fixedValue = "0.0"
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        0.0
                    }

                    // Parse description
                    val description = if (expenseData[1].isNotBlank()) {
                        expenseData[1]
                    } else {
                        val warning = CsvImportWarning(
                            message = "Description is empty, using 'Imported Expense'",
                            lineNumber = lineNumber,
                            fieldName = "description",
                            fieldValue = "",
                            fixedValue = "Imported Expense"
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString())
                        "Imported Expense"
                    }

                    // Parse paidBy
                    val paidBy = if (expenseData[2].isNotBlank()) {
                        // If paidBy is not in the members list, add them automatically
                        if (!members.contains(expenseData[2]) && members.isNotEmpty()) {
                            Log.d(TAG, "Adding user '${expenseData[2]}' from paidBy field to members list at line $lineNumber")
                            // Use a mutable list to add the new member
                            members = members.toMutableList().apply { add(expenseData[2]) }
                        }
                        expenseData[2]
                    } else {
                        // Default to current user if paidBy is empty
                        val defaultUser = currentUser ?: members.firstOrNull() ?: "User"
                        val warning = CsvImportWarning(
                            message = "Paid by is empty, using current user",
                            lineNumber = lineNumber,
                            fieldName = "paidBy",
                            fieldValue = "",
                            fixedValue = defaultUser
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString())
                        defaultUser
                    }

                    // Parse splitBetween
                    val splitBetween = try {
                        val splitList = csvStringToList(expenseData[3])
                        if (splitList.isEmpty()) {
                            // Default to all members if splitBetween is empty
                            val defaultSplit = if (members.isNotEmpty()) members else listOf(paidBy)
                            val warning = CsvImportWarning(
                                message = "Split between is empty, using all members",
                                lineNumber = lineNumber,
                                fieldName = "splitBetween",
                                fieldValue = "",
                                fixedValue = defaultSplit.joinToString("|")
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            defaultSplit
                        } else {
                            // Add any users in splitBetween that are not in the members list
                            if (members.isNotEmpty()) {
                                val newMembers = splitList.filter { !members.contains(it) }
                                if (newMembers.isNotEmpty()) {
                                    Log.d(TAG, "Adding users from splitBetween field to members list at line $lineNumber: ${newMembers.joinToString(", ")}")
                                    // Use a mutable list to add the new members
                                    members = members.toMutableList().apply { addAll(newMembers) }
                                }
                            }
                            splitList
                        }
                    } catch (e: Exception) {
                        // Default to all members if splitBetween parsing fails
                        val defaultSplit = if (members.isNotEmpty()) members else listOf(paidBy)
                        val warning = CsvImportWarning(
                            message = "Failed to parse split between, using all members",
                            lineNumber = lineNumber,
                            fieldName = "splitBetween",
                            fieldValue = expenseData[3],
                            fixedValue = defaultSplit.joinToString("|")
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        defaultSplit
                    }

                    // Parse category
                    val category = if (expenseData[4].isNotBlank()) {
                        // Check if the category exists in the predefined categories
                        val defaultCategories = getDefaultCategories()
                        val categoryExists = defaultCategories.any { it.name == expenseData[4] }
                        if (!categoryExists) {
                            // If category doesn't exist, use "Other" instead
                            Log.d(TAG, "Unknown category '${expenseData[4]}' at line $lineNumber, using 'Other' instead")
                            "Other"
                        } else {
                            expenseData[4]
                        }
                    } else {
                        val warning = CsvImportWarning(
                            message = "Category is empty, using 'None'",
                            lineNumber = lineNumber,
                            fieldName = "category",
                            fieldValue = "",
                            fixedValue = "None"
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString())
                        "None"
                    }

                    // Parse date
                    val date = try {
                        // Check if the expense data has enough fields to include the date
                        if (expenseData.size > 5) {
                            if (expenseData[5].isNotBlank()) {
                                try {
                                    // Try to parse the date if it's not blank
                                    dateFormat.parse(expenseData[5])?.time ?: System.currentTimeMillis()
                                } catch (e: ParseException) {
                                    // If date format is invalid, use current date without warning
                                    val currentDate = System.currentTimeMillis()
                                    Log.d(TAG, "Invalid date format at line $lineNumber, value: '${expenseData[5]}', using current date: ${dateFormat.format(Date(currentDate))}")
                                    currentDate
                                }
                            } else {
                                // If date is empty, use current date without warning
                                val currentDate = System.currentTimeMillis()
                                Log.d(TAG, "Empty date field at line $lineNumber, using current date: ${dateFormat.format(Date(currentDate))}")
                                currentDate
                            }
                        } else {
                            // If date field is missing, use current date without warning
                            val currentDate = System.currentTimeMillis()
                            Log.d(TAG, "Missing date field at line $lineNumber, using current date: ${dateFormat.format(Date(currentDate))}")
                            currentDate
                        }
                    } catch (e: Exception) {
                        // Handle any other exceptions by using current date without warning
                        val currentDate = System.currentTimeMillis()
                        Log.d(TAG, "Error parsing date at line $lineNumber: ${e.message}, using current date: ${dateFormat.format(Date(currentDate))}")
                        currentDate
                    }

                    // Parse isCategoryLocked
                    val isCategoryLocked = try {
                        // Check if the expense data has enough fields to include isCategoryLocked
                        if (expenseData.size > 6) {
                            expenseData[6].toBoolean()
                        } else {
                            // Default to true for existing expenses
                            true
                        }
                    } catch (e: Exception) {
                        // Default to true if parsing fails
                        Log.d(TAG, "Error parsing isCategoryLocked at line $lineNumber: ${e.message}, using default (true)")
                        true
                    }

                    // Always use current timestamp
                    val timestamp = System.currentTimeMillis()

                    // Create expense object
                    expenses.add(
                        Expense(
                            id = expenseId,
                            amount = amount,
                            description = description,
                            paidBy = paidBy,
                            splitBetween = splitBetween,
                            category = category,
                            date = date,
                            timestamp = timestamp,
                            isCategoryLocked = isCategoryLocked
                        )
                    )

                    successfulRows++
                    Log.d(TAG, "Successfully parsed expense at line $lineNumber")
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to create expense object, skipping line: ${e.message}",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Parse member avatars
            val memberAvatars = mutableMapOf<String, String>()
            if (groupData.size > 2 && groupData[2].isNotBlank()) {
                try {
                    val avatarsEntries = groupData[2].split(";")
                    avatarsEntries.forEach { entry ->
                        val parts = entry.split("=")
                        if (parts.size == 2) {
                            memberAvatars[parts[0]] = parts[1]
                        }
                    }
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse member avatars, using empty map",
                        lineNumber = 2,
                        fieldName = "memberAvatars",
                        fieldValue = groupData[2],
                        fixedValue = "{}"
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Parse categories
            var categories = getDefaultCategories()
            if (groupData.size > 3 && groupData[3].isNotBlank()) {
                try {
                    val categoryEntries = groupData[3].split(";")
                    val parsedCategories = mutableListOf<com.example.splitexpenses.data.Category>()

                    categoryEntries.forEach { entry ->
                        val parts = entry.split("~")
                        if (parts.size >= 2) {
                            val name = parts[0]
                            val emoji = parts[1]
                            val keywords = if (parts.size > 2 && parts[2].isNotBlank()) {
                                parts[2].split("^")
                            } else {
                                emptyList()
                            }
                            parsedCategories.add(com.example.splitexpenses.data.Category(name, emoji, keywords))
                        }
                    }

                    if (parsedCategories.isNotEmpty()) {
                        categories = parsedCategories
                    }
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse categories, using default categories",
                        lineNumber = 2,
                        fieldName = "categories",
                        fieldValue = groupData[3]
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Create the group object
            Log.d(TAG, "Creating group object with ${expenses.size} expenses")
            val group = GroupData(
                id = groupId,
                name = groupName,
                members = members,
                expenses = expenses,
                memberUidMap = emptyMap(), // Will be populated by the repository
                allowedUsers = emptyList(), // Will be populated by the repository
                creatorUid = "", // Will be populated by the repository
                categories = categories,
                memberAvatars = memberAvatars
            )

            // Return successful result
            Log.d(TAG, "CSV import completed successfully with ${warnings.size} warnings")
            return CsvImportResult(
                success = true,
                group = group,
                warnings = warnings,
                totalRowsProcessed = totalRowsProcessed,
                successfulRows = successfulRows
            )

        } catch (e: Exception) {
            // Handle any unexpected exceptions
            val error = CsvImportError(
                message = "Unexpected error during CSV import: ${e.message}",
                exception = e
            )
            errors.add(error)
            Log.e(TAG, "Import failed with unexpected error", e)
            return CsvImportResult(
                success = false,
                errors = errors,
                warnings = warnings,
                totalRowsProcessed = totalRowsProcessed,
                successfulRows = successfulRows
            )
        } finally {
            // Close the input stream
            try {
                inputStream.close()
                Log.d(TAG, "Input stream closed")
            } catch (e: Exception) {
                Log.e(TAG, "Error closing input stream", e)
            }
        }
    }
}
