package com.example.splitexpenses.data.repositories

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.LocalDataSource
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayOutputStream
import java.util.UUID

class GroupRepositoryTest {
    
    private lateinit var dataSource: DataSource
    private lateinit var localDataSource: LocalDataSource
    private lateinit var groupRepository: GroupRepository
    
    private val testGroupId = "test-group-id"
    private val testGroup = GroupData(
        id = testGroupId,
        name = "Test Group",
        members = listOf("<PERSON>", "<PERSON>", "<PERSON>"),
        expenses = emptyList(),
        currentUser = "Alice",
        allowedUsers = listOf("device1", "device2", "device3")
    )
    
    private val testDeviceUid = "device1"
    
    @Before
    fun setup() {
        dataSource = mockk(relaxed = true)
        localDataSource = mockk(relaxed = true)
        
        // Mock the localDataSource.getDeviceUid() method
        every { localDataSource.getDeviceUid() } returns testDeviceUid
        
        groupRepository = GroupRepository(dataSource, localDataSource)
    }
    
    @Test
    fun `createGroup should create a new group with the specified parameters`() = runTest {
        // Given
        val name = "New Group"
        val members = listOf("Alice", "Bob")
        val currentUser = "Alice"
        
        // Mock UUID.randomUUID() to return a predictable value
        val mockUuid = "mock-uuid"
        mockkStatic(UUID::class)
        every { UUID.randomUUID().toString() } returns mockUuid
        
        // When
        val groupId = groupRepository.createGroup(name, members, currentUser)
        
        // Then
        assertEquals(mockUuid, groupId)
        
        coVerify { 
            dataSource.saveGroup(match { group ->
                group.id == mockUuid &&
                group.name == name &&
                group.members == members &&
                group.currentUser == currentUser &&
                group.allowedUsers == listOf(testDeviceUid)
            })
        }
        
        verify { localDataSource.saveUserForGroup(mockUuid, currentUser) }
    }
    
    @Test
    fun `joinGroup should update the group with the new member`() = runTest {
        // Given
        val memberName = "David"
        
        // Mock the dataSource.getGroup() method
        coEvery { dataSource.getGroup(testGroupId) } returns testGroup
        
        // When
        groupRepository.joinGroup(testGroupId, memberName)
        
        // Then
        coVerify { 
            dataSource.saveGroup(match { group ->
                group.id == testGroupId &&
                group.members.contains(memberName) &&
                group.currentUser == memberName &&
                group.allowedUsers.contains(testDeviceUid)
            })
        }
        
        verify { localDataSource.saveUserForGroup(testGroupId, memberName) }
    }
    
    @Test
    fun `deleteGroup should delete the specified group`() = runTest {
        // Given
        val groupId = testGroupId
        
        // Set the current group
        val currentGroupFlow = MutableStateFlow(testGroup)
        val field = GroupRepository::class.java.getDeclaredField("_currentGroup")
        field.isAccessible = true
        field.set(groupRepository, currentGroupFlow)
        
        // When
        groupRepository.deleteGroup(groupId)
        
        // Then
        coVerify { dataSource.deleteGroup(groupId) }
        
        // Verify that the current group is cleared if it was the deleted group
        assertNull(groupRepository.currentGroup.first())
    }
    
    @Test
    fun `updateGroupMembers should update the members of the specified group`() = runTest {
        // Given
        val groupId = testGroupId
        val newMembers = listOf("Alice", "Bob", "Charlie", "David")
        
        // When
        groupRepository.updateGroupMembers(groupId, newMembers)
        
        // Then
        coVerify { dataSource.updateGroupField(groupId, "members", newMembers) }
    }
    
    @Test
    fun `exportGroupToCsv should export the current group to CSV format`() = runTest {
        // Given
        val outputStream = ByteArrayOutputStream()
        
        // Set the current group
        val currentGroupFlow = MutableStateFlow(testGroup)
        val field = GroupRepository::class.java.getDeclaredField("_currentGroup")
        field.isAccessible = true
        field.set(groupRepository, currentGroupFlow)
        
        // Mock the CsvUtil.exportGroupToCsv() method
        mockkStatic(com.example.splitexpenses.util.CsvUtil::class)
        every { com.example.splitexpenses.util.CsvUtil.exportGroupToCsv(any(), any()) } returns true
        
        // When
        val result = groupRepository.exportGroupToCsv(outputStream)
        
        // Then
        assertEquals(true, result)
        verify { com.example.splitexpenses.util.CsvUtil.exportGroupToCsv(testGroup, outputStream) }
    }
    
    @Test
    fun `getSavedUserForGroup should return the saved user for the specified group`() {
        // Given
        val groupId = testGroupId
        val savedUser = "Alice"
        
        // Mock the localDataSource.getSavedUserForGroup() method
        every { localDataSource.getSavedUserForGroup(groupId) } returns savedUser
        
        // When
        val result = groupRepository.getSavedUserForGroup(groupId)
        
        // Then
        assertEquals(savedUser, result)
        verify { localDataSource.getSavedUserForGroup(groupId) }
    }
    
    @Test
    fun `cleanup should clean up resources`() {
        // When
        groupRepository.cleanup()
        
        // Then
        verify { dataSource.cleanup() }
    }
}
