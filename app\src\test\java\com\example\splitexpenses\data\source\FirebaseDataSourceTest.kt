package com.example.splitexpenses.data.source

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.ValueEventListener
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test

class FirebaseDataSourceTest {
    
    private lateinit var databaseReference: DatabaseReference
    private lateinit var groupsReference: DatabaseReference
    private lateinit var groupReference: DatabaseReference
    private lateinit var dataSource: FirebaseDataSource
    
    private val testGroupId = "test-group-id"
    private val testGroup = GroupData(
        id = testGroupId,
        name = "Test Group",
        members = listOf("<PERSON>", "<PERSON>", "<PERSON>"),
        expenses = emptyList(),
        currentUser = "Alice",
        allowedUsers = listOf("device1", "device2", "device3")
    )
    
    @Before
    fun setup() {
        // Mock Firebase references
        databaseReference = mockk(relaxed = true)
        groupsReference = mockk(relaxed = true)
        groupReference = mockk(relaxed = true)
        
        // Set up the reference chain
        every { databaseReference.child("groups") } returns groupsReference
        every { groupsReference.child(testGroupId) } returns groupReference
        
        // Create a custom FirebaseDataSource with the mocked database reference
        dataSource = object : FirebaseDataSource() {
            override val database: DatabaseReference
                get() = databaseReference
        }
    }
    
    @Test
    fun `getGroup should return the group data when it exists`() = runTest {
        // Given
        val snapshot = mockk<DataSnapshot>()
        every { snapshot.getValue(GroupData::class.java) } returns testGroup
        coEvery { groupReference.get().await() } returns snapshot
        
        // When
        val result = dataSource.getGroup(testGroupId)
        
        // Then
        assertEquals(testGroup, result)
    }
    
    @Test
    fun `getGroup should return null when the group doesn't exist`() = runTest {
        // Given
        val snapshot = mockk<DataSnapshot>()
        every { snapshot.getValue(GroupData::class.java) } returns null
        coEvery { groupReference.get().await() } returns snapshot
        
        // When
        val result = dataSource.getGroup(testGroupId)
        
        // Then
        assertNull(result)
    }
    
    @Test
    fun `getAvailableGroups should return all available groups`() = runTest {
        // Given
        val snapshot = mockk<DataSnapshot>()
        val child1 = mockk<DataSnapshot>()
        val child2 = mockk<DataSnapshot>()
        
        val group1 = testGroup
        val group2 = testGroup.copy(id = "test-group-id-2", name = "Test Group 2")
        
        every { snapshot.children } returns listOf(child1, child2)
        every { child1.getValue(GroupData::class.java) } returns group1
        every { child2.getValue(GroupData::class.java) } returns group2
        
        coEvery { groupsReference.get().await() } returns snapshot
        
        // When
        val result = dataSource.getAvailableGroups()
        
        // Then
        assertEquals(2, result.size)
        assertEquals(group1, result[0])
        assertEquals(group2, result[1])
    }
    
    @Test
    fun `observeGroup should emit group data when it changes`() = runTest {
        // Given
        val listenerSlot = slot<ValueEventListener>()
        
        every { 
            groupReference.addValueEventListener(capture(listenerSlot))
        } answers { 
            // Return a mock reference that can be used for removeEventListener
            mockk()
        }
        
        // Start collecting the flow
        val flow = dataSource.observeGroup(testGroupId)
        val collector = launch { flow.collect {} }
        
        // Simulate a data change event
        val snapshot = mockk<DataSnapshot>()
        every { snapshot.getValue(GroupData::class.java) } returns testGroup
        listenerSlot.captured.onDataChange(snapshot)
        
        // When
        val result = flow.first()
        
        // Then
        assertEquals(testGroup, result)
        
        // Clean up
        collector.cancel()
    }
    
    @Test
    fun `saveGroup should save the group data`() = runTest {
        // Given
        coEvery { groupReference.setValue(any()).await() } returns mockk()
        
        // When
        dataSource.saveGroup(testGroup)
        
        // Then
        coVerify { groupReference.setValue(testGroup).await() }
    }
    
    @Test
    fun `updateGroupField should update the specified field`() = runTest {
        // Given
        val field = "name"
        val value = "Updated Group Name"
        val fieldReference = mockk<DatabaseReference>(relaxed = true)
        
        every { groupReference.child(field) } returns fieldReference
        coEvery { fieldReference.setValue(any()).await() } returns mockk()
        
        // When
        dataSource.updateGroupField(testGroupId, field, value)
        
        // Then
        coVerify { fieldReference.setValue(value).await() }
    }
    
    @Test
    fun `deleteGroup should remove the group`() = runTest {
        // Given
        coEvery { groupReference.removeValue().await() } returns mockk()
        
        // When
        dataSource.deleteGroup(testGroupId)
        
        // Then
        coVerify { groupReference.removeValue().await() }
    }
    
    @Test
    fun `addExpense should add the expense to the group`() = runTest {
        // Given
        val expense = Expense(
            id = "expense1",
            amount = 100.0,
            description = "Dinner",
            paidBy = "Alice",
            splitBetween = listOf("Alice", "Bob", "Charlie"),
            category = "Food",
            date = 1625097600000 // 2021-07-01
        )
        
        val snapshot = mockk<DataSnapshot>()
        every { snapshot.getValue(GroupData::class.java) } returns testGroup
        coEvery { groupReference.get().await() } returns snapshot
        
        // When
        dataSource.addExpense(testGroupId, expense)
        
        // Then
        coVerify { 
            groupReference.child("expenses").setValue(listOf(expense)).await()
        }
    }
    
    @Test
    fun `cleanup should remove all active listeners`() {
        // Given
        val listener = mockk<ValueEventListener>()
        val activeListenersField = FirebaseDataSource::class.java.getDeclaredField("activeListeners")
        activeListenersField.isAccessible = true
        val activeListeners = mutableMapOf<String, ValueEventListener>()
        activeListeners["group_$testGroupId"] = listener
        activeListenersField.set(dataSource, activeListeners)
        
        // When
        dataSource.cleanup()
        
        // Then
        verify { groupReference.removeEventListener(listener) }
        assertEquals(0, activeListeners.size)
    }
}
