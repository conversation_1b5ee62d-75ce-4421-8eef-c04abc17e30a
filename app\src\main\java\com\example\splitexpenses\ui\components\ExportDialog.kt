package com.example.splitexpenses.ui.components

import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import java.io.FileOutputStream
import java.io.OutputStream

/**
 * Dialog for exporting group data to CSV
 */
@Composable
fun ExportDialog(
    groupName: String,
    onDismiss: () -> Unit,
    onExport: (OutputStream) -> Boolean
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var isExporting by remember { mutableStateOf(false) }
    var exportError by remember { mutableStateOf<String?>(null) }
    var exportSuccess by remember { mutableStateOf(false) }

    // Create a file picker launcher
    val createDocumentLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("text/csv")
    ) { uri ->
        if (uri != null) {
            scope.launch {
                isExporting = true
                exportError = null

                try {
                    // Get output stream from the URI
                    val outputStream = context.contentResolver.openOutputStream(uri)

                    if (outputStream != null) {
                        // Export the group data
                        val success = onExport(outputStream)

                        if (success) {
                            exportSuccess = true
                        } else {
                            exportError = "Failed to export data. Please try again."
                        }
                    } else {
                        exportError = "Could not access the selected file."
                    }
                } catch (e: Exception) {
                    exportError = "Error: ${e.message}"
                } finally {
                    isExporting = false
                }
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = {
            Text(
                text = "Export Group Data",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (isExporting) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Exporting data...")
                } else if (exportSuccess) {
                    Text(
                        "Group data exported successfully!",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                } else if (exportError != null) {
                    Text(
                        exportError!!,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            exportError = null
                            createDocumentLauncher.launch("${groupName.replace(" ", "_")}_expenses.csv")
                        }
                    ) {
                        Text("Try Again")
                    }
                } else {
                    Text(
                        "Export all expense data for this group to a CSV file. " +
                        "This file can be opened in spreadsheet applications or imported back into the app.",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            createDocumentLauncher.launch("${groupName.replace(" ", "_")}_expenses.csv")
                        }
                    ) {
                        Text("Select Export Location")
                    }
                }
            }
        },
        confirmButton = {
            if (exportSuccess) {
                Button(onClick = onDismiss) {
                    Text("Close")
                }
            }
        },
        dismissButton = {
            if (!exportSuccess) {
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        }
    )
}
