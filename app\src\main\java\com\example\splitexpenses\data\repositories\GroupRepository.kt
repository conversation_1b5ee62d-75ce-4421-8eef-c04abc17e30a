package com.example.splitexpenses.data.repositories

import java.io.InputStream
import java.io.OutputStream
import java.util.Date
import java.util.UUID

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.runBlocking

import android.content.Context
import android.util.Log

import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.LocalDataSource
import com.example.splitexpenses.util.CsvImportResult
import com.example.splitexpenses.util.CsvUtil
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing groups with offline capability
 */
@Singleton
class GroupRepository @Inject constructor(
    private val offlineCapableRepository: OfflineCapableRepository,
    private val localDataSource: LocalDataSource
) {

    /**
     * Get the application context
     * @return The application context
     */
    fun getContext(): Context {
        return localDataSource.getContext()
    }
    private val TAG = "GroupRepository"

    // Current group state
    private val _currentGroup = MutableStateFlow<GroupData?>(null)
    val currentGroup: StateFlow<GroupData?> = _currentGroup.asStateFlow()

    // Available groups state
    private val _availableGroups = MutableStateFlow<List<GroupData>>(emptyList())
    val availableGroups: StateFlow<List<GroupData>> = _availableGroups.asStateFlow()

    // Error states
    private val _currentGroupError = MutableStateFlow<String?>(null)
    val currentGroupError: StateFlow<String?> = _currentGroupError.asStateFlow()

    private val _groupsError = MutableStateFlow<String?>(null)
    val groupsError: StateFlow<String?> = _groupsError.asStateFlow()

    // Loading states
    private val _isLoadingCurrentGroup = MutableStateFlow(false)
    val isLoadingCurrentGroup: StateFlow<Boolean> = _isLoadingCurrentGroup.asStateFlow()

    private val _isLoadingGroups = MutableStateFlow(false)
    val isLoadingGroups: StateFlow<Boolean> = _isLoadingGroups.asStateFlow()

    // StateFlow to notify when user loses access to current group
    private val _accessLost = MutableStateFlow(false)
    val accessLost: StateFlow<Boolean> = _accessLost.asStateFlow()

    /**
     * Get the most recent expense date for a group
     * @param group The group to check
     * @return The timestamp of the most recent expense, or 0 if there are no expenses
     */
    private fun getMostRecentExpenseDate(group: GroupData): Long {
        val mostRecentDate = group.expenses.maxOfOrNull { it.date } ?: 0L
        Log.d(TAG, "Most recent expense date for group ${group.name} (${group.id}): ${Date(mostRecentDate)}")
        return mostRecentDate
    }

    /**
     * Start listening for real-time updates to available groups
     */
    fun startListeningForAvailableGroups() {
        // Use onEach and launchIn to collect the flow in a coroutine
        offlineCapableRepository.observeAvailableGroups()
            .onEach { groups ->
                // Filter groups to only show those where the current user's UID is in the allowedUsers list
                // If allowedUsers is empty, show the group (for backward compatibility)
                val currentUserUid = localDataSource.getDeviceUid()
                val filteredGroups = groups.filter { group ->
                    group.allowedUsers.isEmpty() || currentUserUid in group.allowedUsers
                }

                // Sort groups by most recent expense date (most recent first)
                val sortedGroups = filteredGroups.sortedByDescending { group ->
                    getMostRecentExpenseDate(group)
                }

                Log.d(TAG, "Sorted groups by most recent expense date: ${sortedGroups.map { it.name }}")
                _availableGroups.value = sortedGroups
            }
            .launchIn(CoroutineScope(Dispatchers.IO))
    }

    /**
     * Start listening for real-time updates to the current group
     * @param groupId The ID of the group to listen to
     */
    fun startListeningForCurrentGroup(groupId: String) {
        // Use onEach and launchIn to collect the flow in a coroutine
        offlineCapableRepository.observeGroup(groupId)
            .onEach { group ->
                if (group != null) {
                    // Check if the current user still has access to this group
                    val currentUserUid = localDataSource.getDeviceUid()
                    val hasAccess = group.allowedUsers.isEmpty() || currentUserUid in group.allowedUsers

                    if (hasAccess) {
                        _currentGroup.value = group
                        _accessLost.value = false
                    } else {
                        // User has lost access to this group
                        Log.d(TAG, "User has lost access to group ${group.id}")
                        Log.d(TAG, "Current user UID: $currentUserUid")
                        Log.d(TAG, "Group allowedUsers: ${group.allowedUsers}")
                        _currentGroup.value = null
                        _accessLost.value = true
                        stopListeningForCurrentGroup()
                    }
                } else {
                    _currentGroup.value = group
                }
            }
            .launchIn(CoroutineScope(Dispatchers.IO))
    }

    /**
     * Stop listening for real-time updates to the current group
     */
    fun stopListeningForCurrentGroup() {
        // The FirebaseDataSource handles removing listeners internally
        // when the flow is cancelled, so we just need to clear the current group
        _currentGroup.value = null
    }

    /**
     * Reset the access lost flag (call this when navigating back to group list)
     */
    fun resetAccessLost() {
        _accessLost.value = false
    }

    /**
     * Create a new group
     * @param name The name of the group
     * @param members The initial members of the group
     * @param currentUser The current user's name
     * @return The ID of the newly created group
     */
    suspend fun createGroup(name: String, members: List<String>, currentUser: String): String {
        val groupId = UUID.randomUUID().toString()
        val deviceUid = localDataSource.getDeviceUid()

        // Create initial memberUidMap with the current user
        val initialMemberUidMap = if (members.contains(currentUser)) {
            mapOf(currentUser to deviceUid)
        } else {
            emptyMap()
        }

        val group = GroupData(
            id = groupId,
            name = name,
            members = members,
            expenses = emptyList(),
            allowedUsers = listOf(deviceUid),
            memberUidMap = initialMemberUidMap,
            creatorUid = deviceUid  // Set the creator UID to the current user's UID
        )

        offlineCapableRepository.saveGroup(group)
        _currentGroup.value = group

        // Save the mapping between the user's name and UID for this group
        localDataSource.saveUserForGroup(groupId, currentUser)

        // Start listening for real-time updates
        startListeningForCurrentGroup(groupId)

        return groupId
    }

    /**
     * Join an existing group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member joining the group
     */
    suspend fun joinGroup(groupId: String, memberName: String) {
        val group = offlineCapableRepository.getGroup(groupId) ?: throw IllegalStateException("Group not found")

        // Add the current user to the group's members if not already present
        val updatedMembers = if (!group.members.contains(memberName)) {
            group.members + memberName
        } else {
            group.members
        }

        // Add the current device to the allowed users list
        val deviceUid = localDataSource.getDeviceUid()
        val updatedAllowedUsers = if (!group.allowedUsers.contains(deviceUid)) {
            group.allowedUsers + deviceUid
        } else {
            group.allowedUsers
        }

        // Update the memberUidMap to include this user
        val updatedMemberUidMap = group.memberUidMap.toMutableMap()
        updatedMemberUidMap[memberName] = deviceUid

        // Update the group with the new member and allowed user
        val updatedGroup = group.copy(
            members = updatedMembers,
            allowedUsers = updatedAllowedUsers,
            memberUidMap = updatedMemberUidMap
        )

        offlineCapableRepository.saveGroup(updatedGroup)
        _currentGroup.value = updatedGroup

        // Save the mapping between the user's name and UID for this group
        localDataSource.saveUserForGroup(groupId, memberName)

        // Start listening for real-time updates
        startListeningForCurrentGroup(groupId)
    }

    /**
     * Delete a group
     * @param groupId The ID of the group to delete
     * @return True if the group was successfully deleted, false otherwise
     */
    suspend fun deleteGroup(groupId: String): Boolean {
        // Get the group data
        val group = offlineCapableRepository.getGroup(groupId) ?: return false

        // Check if the current user is the creator
        val currentUserUid = localDataSource.getDeviceUid()
        if (group.creatorUid != currentUserUid) {
            Log.d(TAG, "deleteGroup: Current user is not the creator of the group. Only the creator can delete the group.")
            return false
        }

        // Delete the group
        offlineCapableRepository.deleteGroup(groupId)

        // Clear current group if it's the one we just deleted
        if (_currentGroup.value?.id == groupId) {
            _currentGroup.value = null
        }

        return true
    }

    /**
     * Update the members of a group
     * @param groupId The ID of the group to update
     * @param members The new list of members
     * @return True if the members were successfully updated, false otherwise
     */
    suspend fun updateGroupMembers(groupId: String, members: List<String>): Boolean {
        // Get the current group data
        val group = offlineCapableRepository.getGroup(groupId) ?: return false

        // Get the current user's UID
        val currentUserUid = localDataSource.getDeviceUid()

        // Check if the current user is the creator or if they're only updating their own name
        val currentUserName = localDataSource.getSavedUserForGroup(groupId)
        val isCreator = group.creatorUid == currentUserUid

        // If the user is not the creator, they can only update their own name
        if (!isCreator) {
            // Get the current members
            val currentMembers = group.members

            // Check if the user is trying to remove members other than themselves
            val removedMembers = currentMembers.filter { it !in members }
            if (removedMembers.isNotEmpty() && (removedMembers.size > 1 || (removedMembers.size == 1 && removedMembers[0] != currentUserName))) {
                Log.d(TAG, "updateGroupMembers: Only the creator can remove members other than themselves")
                return false
            }
        }

        // Update the members list
        offlineCapableRepository.updateGroupField(groupId, "members", members)

        // Clean up the memberUidMap to remove any members that are no longer in the group
        val updatedMemberUidMap = group.memberUidMap.filterKeys { memberName ->
            memberName in members
        }

        // Update the memberUidMap
        offlineCapableRepository.updateGroupField(groupId, "memberUidMap", updatedMemberUidMap)

        return true
    }

    /**
     * Export a group to CSV
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    fun exportGroupToCsv(outputStream: OutputStream): Boolean {
        val group = _currentGroup.value ?: return false
        return CsvUtil.exportGroupToCsv(group, outputStream)
    }

    /**
     * Import a group from CSV
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional)
     * @return The result of the import operation
     */
    suspend fun importGroupFromCsv(inputStream: InputStream, currentUser: String? = null): CsvImportResult {
        try {
            // Parse the CSV data
            val importResult = CsvUtil.importGroupFromCsv(inputStream, currentUser)

            // If import failed, return the result with errors
            if (!importResult.success || importResult.group == null) {
                Log.e(TAG, "CSV import failed: ${importResult.errors.firstOrNull()?.message ?: "Unknown error"}")
                return importResult
            }

            // Create the group in Firebase
            val deviceUid = localDataSource.getDeviceUid()

            // Get the current user from the import result or use the provided one
            val currentUser = currentUser ?: importResult.group.members.firstOrNull() ?: ""

            // Create initial memberUidMap with the current user
            val initialMemberUidMap = if (currentUser.isNotEmpty() &&
                                         importResult.group.members.contains(currentUser)) {
                mapOf(currentUser to deviceUid)
            } else {
                emptyMap()
            }

            // Set the allowedUsers list to include the current user
            // and set the current user as the creator
            val groupWithAllowedUsers = importResult.group.copy(
                allowedUsers = listOf(deviceUid),
                memberUidMap = initialMemberUidMap,
                creatorUid = deviceUid  // Set the creator UID to the current user's UID
            )

            // Save the group
            offlineCapableRepository.saveGroup(groupWithAllowedUsers)

            // Save the mapping between the user's name and UID for this group
            localDataSource.saveUserForGroup(groupWithAllowedUsers.id, currentUser)

            // Set as current group and start listening for updates
            _currentGroup.value = groupWithAllowedUsers
            startListeningForCurrentGroup(groupWithAllowedUsers.id)

            return importResult
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during CSV import", e)
            throw e
        }
    }

    /**
     * Get the saved user name for a group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    fun getSavedUserForGroup(groupId: String): String? {
        return localDataSource.getSavedUserForGroup(groupId)
    }

    /**
     * Gets a list of unassigned member names (members without an associated UID)
     * @param groupId The ID of the group
     * @return List of unassigned member names
     */
    suspend fun getUnassignedMembers(groupId: String): List<String> {
        // Use the getGroupMembersWithStatus function to get consistent results
        val (allMembers, assignedMembers) = getGroupMembersWithStatus(groupId)

        // Return members who don't have an assigned UID yet
        return allMembers.filter { memberName -> memberName !in assignedMembers }
    }

    /**
     * Gets information about all members in a group, with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    suspend fun getGroupMembersWithStatus(groupId: String): Pair<List<String>, List<String>> {
        // Get the group data
        val group = offlineCapableRepository.getGroup(groupId) ?: return Pair(emptyList(), emptyList())

        // Get all member names
        val allMembers = group.members

        // Get the UIDs of all members who have already been assigned
        val assignedMemberNames = mutableListOf<String>()

        // First, find the creator's name
        val creatorUid = group.creatorUid
        if (creatorUid.isNotEmpty()) {
            // Try to find the creator's name from the memberUidMap
            val creatorName = group.memberUidMap.entries.find { it.value == creatorUid }?.key
            if (creatorName != null && creatorName in allMembers) {
                Log.d(TAG, "getGroupMembersWithStatus: Found creator name: $creatorName")
                assignedMemberNames.add(creatorName)
            } else {
                // If we can't find the creator's name in the memberUidMap, try to get it from local storage
                val name = localDataSource.getUserNameForUidInGroup(groupId, creatorUid)
                if (name != null && name in allMembers) {
                    Log.d(TAG, "getGroupMembersWithStatus: Found creator name from local storage: $name")
                    assignedMemberNames.add(name)
                } else {
                    Log.d(TAG, "getGroupMembersWithStatus: Could not find creator name for UID: $creatorUid")
                }
            }
        }

        // Then add all other assigned members
        for (uid in group.allowedUsers) {
            val name = localDataSource.getUserNameForUidInGroup(groupId, uid)
            if (name != null && name !in assignedMemberNames) {
                assignedMemberNames.add(name)
            }
        }

        // Also add all members that have entries in the memberUidMap
        for (memberName in group.memberUidMap.keys) {
            if (memberName in allMembers && memberName !in assignedMemberNames) {
                assignedMemberNames.add(memberName)
            }
        }

        Log.d(TAG, "getGroupMembersWithStatus: All members: $allMembers, Assigned members: $assignedMemberNames")
        return Pair(allMembers, assignedMemberNames)
    }

    /**
     * Check if the current user is the creator of a group
     * This method works for any group, not just the current one
     * @param groupId The ID of the group
     * @return True if the current user is the creator, false otherwise
     */
    fun isCurrentUserGroupCreator(groupId: String): Boolean {
        // Get the current user's UID
        val currentUserUid = localDataSource.getDeviceUid()

        // First check if this is the current group (for efficiency)
        val currentGroup = _currentGroup.value
        if (currentGroup != null && currentGroup.id == groupId) {
            val isCreator = currentGroup.creatorUid == currentUserUid
            Log.d(TAG, "isCurrentUserGroupCreator (current group): Group ID: $groupId, Is Creator: $isCreator")
            return isCreator
        }

        // If not the current group, check in the available groups
        val availableGroup = _availableGroups.value.find { it.id == groupId }
        if (availableGroup != null) {
            val isCreator = availableGroup.creatorUid == currentUserUid
            Log.d(TAG, "isCurrentUserGroupCreator (available group): Group ID: $groupId, Is Creator: $isCreator")
            return isCreator
        }

        // If the group is not loaded yet, fetch it from the offline-capable repository
        return runBlocking {
            try {
                val group = offlineCapableRepository.getGroup(groupId)
                if (group != null) {
                    val isCreator = group.creatorUid == currentUserUid
                    Log.d(TAG, "isCurrentUserGroupCreator (fetched group): Group ID: $groupId, Is Creator: $isCreator")
                    return@runBlocking isCreator
                } else {
                    Log.d(TAG, "isCurrentUserGroupCreator: Group not found: $groupId")
                    return@runBlocking false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking if user is creator: ${e.message}", e)
                return@runBlocking false
            }
        }
    }

    /**
     * Removes a member from a group and kicks them out (removes from allowedUsers)
     * This should only be called by the group creator
     * @param groupId The ID of the group
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    suspend fun removeMemberAndKick(groupId: String, memberName: String): Boolean {
        try {
            // Check if the current user is the creator
            if (!isCurrentUserGroupCreator(groupId)) {
                Log.d(TAG, "removeMemberAndKick: Only the creator can remove members")
                return false
            }

            // Get the group data
            val group = offlineCapableRepository.getGroup(groupId) ?: return false

            // Get the UID for this member name from the memberUidMap (more reliable than local storage)
            val memberUid = group.memberUidMap[memberName]
            Log.d(TAG, "removeMemberAndKick: Member: $memberName, UID: $memberUid")

            // Remove the member from the members list
            val updatedMembers = group.members.filter { it != memberName }
            Log.d(TAG, "removeMemberAndKick: Updating members from ${group.members} to $updatedMembers")
            offlineCapableRepository.updateGroupField(groupId, "members", updatedMembers)

            // Remove the member from the memberUidMap
            val updatedMemberUidMap = group.memberUidMap.toMutableMap()
            updatedMemberUidMap.remove(memberName)
            Log.d(TAG, "removeMemberAndKick: Updating memberUidMap, removed $memberName")
            offlineCapableRepository.updateGroupField(groupId, "memberUidMap", updatedMemberUidMap)

            // Remove the member from the allowedUsers list if we have their UID
            if (memberUid != null) {
                val updatedAllowedUsers = group.allowedUsers.filter { it != memberUid }
                Log.d(TAG, "removeMemberAndKick: Updating allowedUsers from ${group.allowedUsers} to $updatedAllowedUsers")
                offlineCapableRepository.updateGroupField(groupId, "allowedUsers", updatedAllowedUsers)
            } else {
                Log.w(TAG, "removeMemberAndKick: Warning: Could not find UID for member $memberName, cannot remove from allowedUsers")
            }

            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error removing member and kicking", e)
            return false
        }
    }

    /**
     * Load available groups once (for one-time operations)
     */
    suspend fun loadAvailableGroups() {
        try {
            _isLoadingGroups.value = true
            _groupsError.value = null

            val groups = offlineCapableRepository.getAvailableGroups()

            // Filter groups to only show those where the current user's UID is in the allowedUsers list
            // If allowedUsers is empty, show the group (for backward compatibility)
            val currentUserUid = localDataSource.getDeviceUid()
            val filteredGroups = groups.filter { group ->
                group.allowedUsers.isEmpty() || currentUserUid in group.allowedUsers
            }

            // Sort groups by most recent expense date (most recent first)
            val sortedGroups = filteredGroups.sortedByDescending { group ->
                getMostRecentExpenseDate(group)
            }

            Log.d(TAG, "loadAvailableGroups: Sorted groups by most recent expense date: ${sortedGroups.map { it.name }}")
            _availableGroups.value = sortedGroups
        } catch (e: Exception) {
            Log.e(TAG, "Error loading available groups", e)
            _groupsError.value = "Failed to load groups: ${e.message}"
        } finally {
            _isLoadingGroups.value = false
        }
    }

    /**
     * Update the categories for a group
     * @param groupId The ID of the group
     * @param categories The updated list of categories
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateGroupCategories(groupId: String, categories: List<Category>): Boolean {
        try {
            // Get the current group data
            val group = offlineCapableRepository.getGroup(groupId) ?: return false

            // Update the categories field
            offlineCapableRepository.updateGroupField(groupId, "categories", categories)

            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating categories", e)
            return false
        }
    }

    /**
     * Update the group name
     * @param groupId The ID of the group to update
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateGroupName(groupId: String, newName: String): Boolean {
        try {
            // Get the group data
            val group = offlineCapableRepository.getGroup(groupId) ?: return false

            // Check if the current user is the creator
            val currentUserUid = localDataSource.getDeviceUid()
            if (group.creatorUid != currentUserUid) {
                Log.d(TAG, "updateGroupName: Only the creator can update the group name")
                return false
            }

            // Update the name field
            offlineCapableRepository.updateGroupField(groupId, "name", newName)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating group name", e)
            return false
        }
    }

    /**
     * Update a member's avatar
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateMemberAvatar(groupId: String, memberName: String, avatarEmoji: String?): Boolean {
        try {
            // Get the group data
            val group = offlineCapableRepository.getGroup(groupId) ?: return false

            // Check if the member exists in the group
            if (memberName !in group.members) {
                Log.d(TAG, "updateMemberAvatar: Member $memberName not found in group")
                return false
            }

            // Check if the current user is updating their own avatar
            val currentUserName = localDataSource.getSavedUserForGroup(groupId)
            if (currentUserName != memberName) {
                Log.d(TAG, "updateMemberAvatar: Users can only update their own avatar")
                return false
            }

            // Update the memberAvatars map
            val updatedMemberAvatars = group.memberAvatars.toMutableMap()

            if (avatarEmoji == null) {
                // Remove the avatar if null is passed
                updatedMemberAvatars.remove(memberName)
                Log.d(TAG, "updateMemberAvatar: Removing avatar for member $memberName")
            } else {
                // Otherwise update with the new emoji
                updatedMemberAvatars[memberName] = avatarEmoji
                Log.d(TAG, "updateMemberAvatar: Setting avatar for member $memberName to $avatarEmoji")
            }

            Log.d(TAG, "updateMemberAvatar: Updated memberAvatars map: $updatedMemberAvatars")

            // Update the field
            offlineCapableRepository.updateGroupField(groupId, "memberAvatars", updatedMemberAvatars)

            // Also update the current group in memory to ensure UI updates immediately
            _currentGroup.value = _currentGroup.value?.copy(memberAvatars = updatedMemberAvatars)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating member avatar", e)
            return false
        }
    }

    /**
     * Update a member's name while preserving their UID
     * @param groupId The ID of the group
     * @param oldMemberName The current name of the member
     * @param newMemberName The new name for the member
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateMemberName(groupId: String, oldMemberName: String, newMemberName: String): Boolean {
        try {
            // Get the group data
            val group = offlineCapableRepository.getGroup(groupId) ?: return false

            // Check if the member exists in the group
            if (oldMemberName !in group.members) {
                Log.d(TAG, "updateMemberName: Member $oldMemberName not found in group")
                return false
            }

            // Check if the new name is already taken
            if (newMemberName in group.members) {
                Log.d(TAG, "updateMemberName: Name $newMemberName is already taken")
                return false
            }

            // Check if the current user is updating their own name
            val currentUserName = localDataSource.getSavedUserForGroup(groupId)
            if (currentUserName != oldMemberName) {
                Log.d(TAG, "updateMemberName: Users can only update their own name")
                return false
            }

            // Get the current user's UID
            val currentUserUid = localDataSource.getDeviceUid()

            // Update the members list
            val updatedMembers = group.members.map { if (it == oldMemberName) newMemberName else it }

            // Update the memberUidMap
            val updatedMemberUidMap = group.memberUidMap.toMutableMap()
            val memberUid = updatedMemberUidMap.remove(oldMemberName)
            if (memberUid != null) {
                updatedMemberUidMap[newMemberName] = memberUid
            }

            // Update the memberAvatars map if the member has an avatar
            val updatedMemberAvatars = group.memberAvatars.toMutableMap()
            val memberAvatar = updatedMemberAvatars.remove(oldMemberName)
            if (memberAvatar != null) {
                updatedMemberAvatars[newMemberName] = memberAvatar
            }

            // Update all fields
            val updatedGroup = group.copy(
                members = updatedMembers,
                memberUidMap = updatedMemberUidMap,
                memberAvatars = updatedMemberAvatars
            )
            offlineCapableRepository.saveGroup(updatedGroup)

            // Update the user preferences
            localDataSource.saveUserForGroup(groupId, newMemberName)

            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating member name", e)
            return false
        }
    }

    /**
     * Get the avatar for a member
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    fun getMemberAvatar(groupId: String, memberName: String): String? {
        val group = _currentGroup.value
        if (group?.id != groupId) {
            Log.d(TAG, "getMemberAvatar: Group ID mismatch - current: ${group?.id}, requested: $groupId")
            return null
        }

        // Return the avatar from the memberAvatars map
        // If the member has no avatar entry, return null to display the default account_outline
        val avatar = group.memberAvatars[memberName]
        Log.d(TAG, "getMemberAvatar: Avatar for $memberName = $avatar, memberAvatars = ${group.memberAvatars}")
        return avatar
    }

    /**
     * Get pending sync count for offline changes
     */
    fun getPendingSyncCountFlow() = offlineCapableRepository.getPendingSyncCountFlow()

    /**
     * Force sync offline changes
     */
    suspend fun forceSyncOfflineChanges() = offlineCapableRepository.forceSyncOfflineChanges()
}
