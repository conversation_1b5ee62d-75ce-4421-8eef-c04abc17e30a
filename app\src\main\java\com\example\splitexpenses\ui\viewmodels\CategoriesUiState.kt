package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.Category

/**
 * UI state for the categories management screen
 */
data class CategoriesUiState(
    val categories: List<Category> = emptyList(),
    val newCategoryName: String = "",
    val newCategoryEmoji: String = "",
    val newCategoryKeywords: String = "",
    val showError: Boolean = false,
    val editingCategory: Category? = null,
    val editName: String = "",
    val editEmoji: String = "",
    val editKeywords: String = "",
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState
