package com.example.splitexpenses.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.ui.viewmodels.CategoriesViewModel


@Composable
fun ManageCategoriesScreen(
    onBackClick: () -> Unit,
    viewModel: CategoriesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    // Handle back navigation with save
    val handleBackWithSave = {
        viewModel.saveCategories()
        onBackClick()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = handleBackWithSave) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Manage Categories",
                style = MaterialTheme.typography.headlineLarge,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(48.dp))
        }

        Spacer(modifier = Modifier.height(16.dp))
            // Add new category section
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = MaterialTheme.shapes.medium,
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer),
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Add New Category",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Emoji and Category Name on the same row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Square emoji field
                        OutlinedTextField(
                            value = uiState.newCategoryEmoji,
                            onValueChange = {viewModel.updateNewCategoryEmoji(it)},
                            label = { Text(text = "",style= MaterialTheme.typography.titleMedium) },
                            modifier = Modifier
                                .size(width = 56.dp, height = 64.dp),
                            singleLine = true,
                            textStyle = MaterialTheme.typography.titleMedium.copy(
                                textAlign = TextAlign.Center
                            ),
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )

                        // Category name field takes remaining space
                        OutlinedTextField(
                            value = uiState.newCategoryName,
                            onValueChange = { viewModel.updateNewCategoryName(it) },
                            label = { Text("Category Name") },
                            modifier = Modifier.weight(1f),
                            singleLine = true,
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = uiState.newCategoryKeywords,
                        onValueChange = { viewModel.updateNewCategoryKeywords(it) },
                        label = { Text("Keywords (comma-separated)") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )

                    if (uiState.showError) {
                        Text(
                            text = "All fields are required",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick =  {
                            viewModel.addCategory()
                            viewModel.saveCategories()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Add Category")
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Categories list
            Text(
                text = "Current Categories",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(uiState.categories) { category ->
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        shape = MaterialTheme.shapes.small,
                        color = MaterialTheme.colorScheme.surface,
                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Text(
                                        text = category.emoji,
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                    Text(
                                        text = category.name,
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                }

                                if (category.name != "None") { // Don't allow deleting the "None" category
                                    Row {
                                        IconButton(
                                            onClick = {
                                                viewModel.startEditingCategory(category)
                                            }
                                        ) {
                                            Icon(
                                                Icons.Default.Edit,
                                                contentDescription = "Edit category",
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                        IconButton(
                                            onClick = {
                                                viewModel.deleteCategory(category)
                                            }
                                        ) {
                                            Icon(
                                                Icons.Default.Delete,
                                                contentDescription = "Remove category",
                                                tint = MaterialTheme.colorScheme.error
                                            )
                                        }
                                    }
                                }
                            }

                            // Show keywords for the category
                            if (category.keywords.isNotEmpty()) {
                                Text(
                                    text = "Keywords: ${category.keywords.joinToString(", ")}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(start = 32.dp, top = 4.dp)
                                )
                            }
                        }
                    }
                }
            }

        // Edit category dialog
        if (uiState.editingCategory != null) {
            AlertDialog(
                onDismissRequest = { viewModel.cancelEditingCategory() },
                containerColor = MaterialTheme.colorScheme.surface,
                tonalElevation = 8.dp,
                title = { Text("Edit Category") },
                text = {
                    Column {
                        // Emoji and Category Name on the same row
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Square emoji field
                            OutlinedTextField(
                                value = uiState.editEmoji,
                                onValueChange = {viewModel.updateEditEmoji(it)},
                                label = { Text(text = "",style= MaterialTheme.typography.titleMedium) },
                                modifier = Modifier
                                    .size(width = 56.dp, height = 64.dp),
                                singleLine = true,
                                textStyle = MaterialTheme.typography.titleMedium.copy(
                                    textAlign = TextAlign.Center
                                ),
                                colors = TextFieldDefaults.colors(
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                )
                            )
//                            // Square emoji field
//                            OutlinedTextField(
//                                value = uiState.editEmoji,
//                                onValueChange = { newValue ->
//                                    // Filter input to only allow emojis (based on blog approach)
//                                    val filtered = StringBuilder()
//                                    for (char in newValue) {
//                                        val codePoint = char.code
//                                        if (codePoint in 0x1F600..0x1F64F || // Emoticons
//                                            codePoint in 0x1F300..0x1F5FF || // Misc Symbols and Pictographs
//                                            codePoint in 0x1F680..0x1F6FF || // Transport and Map
//                                            codePoint in 0x1F1E6..0x1F1FF || // Regional indicators
//                                            codePoint in 0x2600..0x26FF ||   // Misc symbols
//                                            codePoint in 0x2700..0x27BF ||   // Dingbats
//                                            codePoint in 0xFE00..0xFE0F ||   // Variation Selectors
//                                            codePoint in 0x1F900..0x1F9FF || // Supplemental Symbols and Pictographs
//                                            codePoint in 0x1F018..0x1F270 || // Various symbols
//                                            Character.getType(char) == Character.OTHER_SYMBOL.toInt()
//                                        ) {
//                                            filtered.append(char)
//                                        }
//                                    }
//                                    viewModel.updateEditEmoji(filtered.toString())
//                                },
//                                label = { Text("Emoji") },
//                                modifier = Modifier
//                                    .size(64.dp), // Square shape
//                                singleLine = true,
//                                textStyle = MaterialTheme.typography.titleLarge.copy(
//                                    textAlign = TextAlign.Center
//                                )
//                            )

                            // Category name field takes remaining space
                            OutlinedTextField(
                                value = uiState.editName,
                                onValueChange = { viewModel.updateEditName(it) },
                                label = { Text("Category Name") },
                                modifier = Modifier.weight(1f),
                                singleLine = true,
                                colors = TextFieldDefaults.colors(
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                )
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        OutlinedTextField(
                            value = uiState.editKeywords,
                            onValueChange = { viewModel.updateEditKeywords(it) },
                            label = { Text("Keywords (comma-separated)") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )
                    }
                },
                confirmButton = {
                    Button(
                        onClick = {
                            viewModel.saveEditedCategory()
                        }
                    ) {
                        Text("Save")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { viewModel.cancelEditingCategory() }) {
                        Text("Cancel")
                    }
                }
            )
        }
    }
}
