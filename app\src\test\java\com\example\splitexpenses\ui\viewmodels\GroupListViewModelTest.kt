package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.GroupRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

@ExperimentalCoroutinesApi
class GroupListViewModelTest {

    private lateinit var groupRepository: GroupRepository
    private lateinit var viewModel: GroupListViewModel

    private val testDispatcher = StandardTestDispatcher()

    private val testGroups = listOf(
        GroupData(
            id = "group1",
            name = "Group 1",
            members = listOf("Alice", "Bob"),
            expenses = emptyList(),
            currentUser = "Alice",
            allowedUsers = listOf("device1", "device2")
        ),
        GroupData(
            id = "group2",
            name = "Group 2",
            members = listOf("Alice", "Charlie"),
            expenses = emptyList(),
            currentUser = "Alice",
            allowedUsers = listOf("device1", "device3")
        )
    )

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        groupRepository = mockk(relaxed = true)

        // Mock the availableGroups flow in the groupRepository
        val availableGroupsFlow = MutableStateFlow(testGroups)
        every { groupRepository.availableGroups } returns availableGroupsFlow

        // Mock the isLoadingGroups flow
        val isLoadingGroupsFlow = MutableStateFlow(false)
        every { groupRepository.isLoadingGroups } returns isLoadingGroupsFlow

        // Mock the groupsError flow
        val groupsErrorFlow = MutableStateFlow<String?>(null)
        every { groupRepository.groupsError } returns groupsErrorFlow

        viewModel = GroupListViewModel(groupRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state should have empty selected groups and multi-select mode disabled`() {
        // Then
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedGroups)
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertFalse(viewModel.uiState.value.isLoading)
        assertEquals(null, viewModel.uiState.value.error)
    }

    @Test
    fun `constructor should start listening for available groups`() {
        // Then
        verify { groupRepository.startListeningForAvailableGroups() }
    }

    @Test
    fun `joinGroup should call repository with correct parameters`() = runTest {
        // Given
        val groupId = "group1"
        val memberName = "Alice"

        // When
        viewModel.joinGroup(groupId, memberName)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { groupRepository.joinGroup(groupId, memberName) }
    }

    @Test
    fun `createGroup should call repository with correct parameters`() = runTest {
        // Given
        val name = "New Group"
        val members = listOf("Alice", "Bob", "Charlie")
        val currentUser = "Alice"

        // When
        viewModel.createGroup(name, members, currentUser)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { groupRepository.createGroup(name, members, currentUser) }
    }

    @Test
    fun `deleteGroups should call repository and update UI state`() = runTest {
        // Given
        val groupIds = setOf("group1", "group2")

        // Set initial state with selected groups and multi-select mode enabled
        viewModel.setMultiSelectMode(true)
        viewModel.toggleGroupSelection("group1", true)
        viewModel.toggleGroupSelection("group2", true)

        // When
        viewModel.deleteGroups(groupIds)
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify {
            groupRepository.deleteGroup("group1")
            groupRepository.deleteGroup("group2")
        }

        // Verify UI state is updated
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedGroups)
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
    }

    @Test
    fun `setMultiSelectMode should update UI state`() {
        // When
        viewModel.setMultiSelectMode(true)

        // Then
        assertTrue(viewModel.uiState.value.isMultiSelectMode)

        // When
        viewModel.setMultiSelectMode(false)

        // Then
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertEquals(emptySet<String>(), viewModel.uiState.value.selectedGroups)
    }

    @Test
    fun `toggleGroupSelection should update selected groups`() {
        // Given
        val groupId = "group1"

        // When - Select group
        viewModel.toggleGroupSelection(groupId, true)

        // Then
        assertTrue(groupId in viewModel.uiState.value.selectedGroups)

        // When - Deselect group
        viewModel.toggleGroupSelection(groupId, false)

        // Then
        assertFalse(groupId in viewModel.uiState.value.selectedGroups)
    }

    @Test
    fun `toggleGroupSelection should disable multi-select mode when all items are deselected`() {
        // Given - Enable multi-select mode and select a group
        viewModel.setMultiSelectMode(true)
        val groupId = "group1"
        viewModel.toggleGroupSelection(groupId, true)

        // Verify initial state
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(groupId in viewModel.uiState.value.selectedGroups)

        // When - Deselect the last selected group
        viewModel.toggleGroupSelection(groupId, false)

        // Then - Multi-select mode should be automatically disabled
        assertFalse(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(viewModel.uiState.value.selectedGroups.isEmpty())
    }

    @Test
    fun `toggleGroupSelection should keep multi-select mode when items remain selected`() {
        // Given - Enable multi-select mode and select multiple groups
        viewModel.setMultiSelectMode(true)
        val groupId1 = "group1"
        val groupId2 = "group2"
        viewModel.toggleGroupSelection(groupId1, true)
        viewModel.toggleGroupSelection(groupId2, true)

        // Verify initial state
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertEquals(2, viewModel.uiState.value.selectedGroups.size)

        // When - Deselect one group (but not all)
        viewModel.toggleGroupSelection(groupId1, false)

        // Then - Multi-select mode should remain enabled
        assertTrue(viewModel.uiState.value.isMultiSelectMode)
        assertTrue(groupId2 in viewModel.uiState.value.selectedGroups)
        assertFalse(groupId1 in viewModel.uiState.value.selectedGroups)
    }

    @Test
    fun `getSavedUserForGroup should call repository with correct parameters`() {
        // Given
        val groupId = "group1"
        val savedUser = "Alice"

        // Mock the getSavedUserForGroup method
        every { groupRepository.getSavedUserForGroup(groupId) } returns savedUser

        // When
        val result = viewModel.getSavedUserForGroup(groupId)

        // Then
        verify { groupRepository.getSavedUserForGroup(groupId) }
        assertEquals(savedUser, result)
    }
}
