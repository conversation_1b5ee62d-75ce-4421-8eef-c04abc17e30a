package com.example.splitexpenses.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.repositories.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the categories management screen
 */
@HiltViewModel
class CategoriesViewModel @Inject constructor(
    private val groupRepository: GroupRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoriesUiState())
    val uiState: StateFlow<CategoriesUiState> = _uiState.asStateFlow()

    init {
        // Load categories from the current group
        viewModelScope.launch {
            groupRepository.currentGroup.collectLatest { group ->
                if (group != null) {
                    _uiState.update {
                        it.copy(categories = group.categories)
                    }
                }
            }
        }
    }

    /**
     * Update the new category name
     */
    fun updateNewCategoryName(name: String) {
        _uiState.update { it.copy(newCategoryName = name) }
    }

    /**
     * Update the new category emoji
     */
    fun updateNewCategoryEmoji(emoji: String) {
        if (emoji.length <= 2) {
            _uiState.update { it.copy(newCategoryEmoji = emoji) }
        }
    }

    /**
     * Update the new category keywords
     */
    fun updateNewCategoryKeywords(keywords: String) {
        _uiState.update { it.copy(newCategoryKeywords = keywords) }
    }

    /**
     * Add a new category
     */
    fun addCategory() {
        val currentState = uiState.value

        if (currentState.newCategoryName.isBlank() ||
            currentState.newCategoryEmoji.isBlank() ||
            currentState.newCategoryKeywords.isBlank()) {
            _uiState.update { it.copy(showError = true) }
            return
        }

        val keywords = currentState.newCategoryKeywords
            .split(",")
            .map { it.trim() }
            .filter { it.isNotEmpty() }

        val newCategory = Category(
            name = currentState.newCategoryName,
            emoji = currentState.newCategoryEmoji,
            keywords = keywords
        )

        val updatedCategories = currentState.categories + newCategory

        _uiState.update {
            it.copy(
                categories = updatedCategories,
                newCategoryName = "",
                newCategoryEmoji = "",
                newCategoryKeywords = "",
                showError = false
            )
        }
    }

    /**
     * Delete a category
     */
    fun deleteCategory(category: Category) {
        val currentState = uiState.value
        val updatedCategories = currentState.categories.filter { it != category }

        _uiState.update { it.copy(categories = updatedCategories) }
    }

    /**
     * Start editing a category
     */
    fun startEditingCategory(category: Category) {
        _uiState.update {
            it.copy(
                editingCategory = category,
                editName = category.name,
                editEmoji = category.emoji,
                editKeywords = category.keywords.joinToString(", ")
            )
        }
    }

    /**
     * Update the edit name
     */
    fun updateEditName(name: String) {
        _uiState.update { it.copy(editName = name) }
    }

    /**
     * Update the edit emoji
     */
    fun updateEditEmoji(emoji: String) {
        if (emoji.length <= 2) {
            _uiState.update { it.copy(editEmoji = emoji) }
        }
    }

    /**
     * Update the edit keywords
     */
    fun updateEditKeywords(keywords: String) {
        _uiState.update { it.copy(editKeywords = keywords) }
    }

    /**
     * Save the edited category
     */
    fun saveEditedCategory() {
        val currentState = uiState.value
        val editingCategory = currentState.editingCategory ?: return

        val keywords = currentState.editKeywords
            .split(",")
            .map { it.trim() }
            .filter { it.isNotEmpty() }

        val updatedCategory = Category(
            name = currentState.editName,
            emoji = currentState.editEmoji,
            keywords = keywords
        )

        val updatedCategories = currentState.categories.map {
            if (it == editingCategory) updatedCategory else it
        }

        _uiState.update {
            it.copy(
                categories = updatedCategories,
                editingCategory = null
            )
        }
    }

    /**
     * Cancel editing a category
     */
    fun cancelEditingCategory() {
        _uiState.update { it.copy(editingCategory = null) }
    }

    /**
     * Save all categories to the current group
     */
    fun saveCategories() {
        viewModelScope.launch {
            try {
                val currentGroup = groupRepository.currentGroup.value ?: return@launch

                // Update the group with the new categories
                groupRepository.updateGroupCategories(currentGroup.id, uiState.value.categories)
            } catch (e: Exception) {
                println("Error saving categories: ${e.message}")
                _uiState.update { it.copy(error = "Failed to save categories: ${e.message}") }
            }
        }
    }

    /**
     * Clear the error state
     */
    fun clearError() {
        _uiState.update { it.copy(showError = false) }
    }
}
