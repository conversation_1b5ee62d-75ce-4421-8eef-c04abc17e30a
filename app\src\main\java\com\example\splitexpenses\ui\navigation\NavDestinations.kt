package com.example.splitexpenses.ui.navigation

/**
 * Navigation destinations for the app
 */
object NavDestinations {
    const val GROUP_LIST_ROUTE = "group_list"
    const val EXPENSE_LIST_ROUTE = "expense_list"
    const val EXPENSE_DETAILS_ROUTE = "expense_details"
    const val EXPENSE_EDIT_ROUTE = "expense_edit"
    const val BALANCE_DETAILS_ROUTE = "balance_details"
    const val STATISTICS_ROUTE = "statistics"
    const val MANAGE_CATEGORIES_ROUTE = "manage_categories"

    // Route with parameters
    const val EXPENSE_DETAILS_ROUTE_WITH_PARAMS = "$EXPENSE_DETAILS_ROUTE/{expenseId}"
    const val EXPENSE_EDIT_ROUTE_WITH_PARAMS = "$EXPENSE_EDIT_ROUTE?expenseId={expenseId}"
    const val EXPENSE_LIST_ROUTE_WITH_PARAMS = "$EXPENSE_LIST_ROUTE/{groupId}"

    // Arguments
    const val EXPENSE_ID_ARG = "expenseId"
    const val GROUP_ID_ARG = "groupId"
}
