package com.example.splitexpenses.ui.components

import kotlinx.coroutines.launch

import android.net.Uri

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

import com.example.splitexpenses.util.CsvImportError
import com.example.splitexpenses.util.CsvImportResult

/**
 * Dialog for importing group data from CSV
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImportDialog(
    onDismiss: () -> Unit,
    onImport: suspend (Uri, String?) -> CsvImportResult
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var isImporting by remember { mutableStateOf(false) }
    var importResult by remember { mutableStateOf<CsvImportResult?>(null) }
    var selectedFileUri by remember { mutableStateOf<Uri?>(null) }
    var selectedFileName by remember { mutableStateOf<String?>(null) }
    var yourName by remember { mutableStateOf("") }
    var showNameField by remember { mutableStateOf(false) }
    var showImportDetails by remember { mutableStateOf(false) }

    // Create a file picker launcher
    val openDocumentLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri ->
        if (uri != null) {
            selectedFileUri = uri

            // Try to get the file name
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val displayNameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                    if (displayNameIndex != -1) {
                        selectedFileName = cursor.getString(displayNameIndex)
                    }
                }
            }

            // Show the name field after selecting a file
            showNameField = true
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = {
            Text(
                text = "Import Group from CSV",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (isImporting) {
                    // Loading state
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Importing data...")
                } else if (importResult != null) {
                    // Import completed (success or failure)
                    if (showImportDetails) {
                        // Show detailed import results
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // Summary header
                            Text(
                                text = if (importResult!!.success) "Import Successful" else "Import Failed",
                                style = MaterialTheme.typography.titleLarge,
                                color = if (importResult!!.success)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.error
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Summary text
                            Text(
                                text = importResult!!.getSummary(),
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // Group details if successful
                            if (importResult!!.success && importResult!!.group != null) {
                                Card(
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Column(
                                        modifier = Modifier.padding(16.dp)
                                    ) {
                                        Text(
                                            text = "Imported Group",
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.Bold
                                        )

                                        Spacer(modifier = Modifier.height(8.dp))

                                        Text("Name: ${importResult!!.group!!.name}")
                                        Text("Members: ${importResult!!.group!!.members.joinToString(", ")}")
                                        Text("Expenses: ${importResult!!.group!!.expenses.size}")
                                    }
                                }

                                Spacer(modifier = Modifier.height(16.dp))
                            }

                            // Errors and warnings
                            if (importResult!!.hasErrors || importResult!!.hasWarnings) {
                                Card(
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Column(
                                        modifier = Modifier.padding(16.dp)
                                    ) {
                                        if (importResult!!.hasErrors) {
                                            Text(
                                                text = "Errors (${importResult!!.errors.size})",
                                                style = MaterialTheme.typography.titleMedium,
                                                color = MaterialTheme.colorScheme.error,
                                                fontWeight = FontWeight.Bold
                                            )

                                            Spacer(modifier = Modifier.height(8.dp))

                                            // Create a scrollable container for errors if there are more than 3
                                            if (importResult!!.errors.size > 3) {
                                                Box(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        // Set a fixed height for the scrollable area
                                                        .height(120.dp)
                                                        // Add a thin border to indicate scrollable area
                                                        .border(
                                                            width = 1.dp,
                                                            color = MaterialTheme.colorScheme.error.copy(alpha = 0.3f),
                                                            shape = MaterialTheme.shapes.small
                                                        )
                                                ) {
                                                    Column(
                                                        modifier = Modifier
                                                            .fillMaxSize()
                                                            .padding(8.dp)
                                                            .verticalScroll(rememberScrollState())
                                                    ) {
                                                        // Show all errors in the scrollable container
                                                        importResult!!.errors.forEach { error ->
                                                            Text(
                                                                text = "• ${error.toString()}",
                                                                style = MaterialTheme.typography.bodySmall,
                                                                color = MaterialTheme.colorScheme.error
                                                            )
                                                            Spacer(modifier = Modifier.height(4.dp))
                                                        }
                                                    }
                                                }
                                            } else {
                                                // If there are only a few errors, show them directly
                                                importResult!!.errors.forEach { error ->
                                                    Text(
                                                        text = "• ${error.toString()}",
                                                        style = MaterialTheme.typography.bodySmall,
                                                        color = MaterialTheme.colorScheme.error
                                                    )
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                }
                                            }

                                            if (importResult!!.hasWarnings) {
                                                Spacer(modifier = Modifier.height(8.dp))
                                                Divider()
                                                Spacer(modifier = Modifier.height(8.dp))
                                            }
                                        }

                                        if (importResult!!.hasWarnings) {
                                            Text(
                                                text = "Warnings (${importResult!!.warnings.size})",
                                                style = MaterialTheme.typography.titleMedium,
                                                color = MaterialTheme.colorScheme.tertiary,
                                                fontWeight = FontWeight.Bold
                                            )

                                            Spacer(modifier = Modifier.height(8.dp))

                                            // Create a scrollable container for warnings
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    // Set a fixed height for the scrollable area
                                                    .height(180.dp)
                                                    // Add a thin border to indicate scrollable area
                                                    .border(
                                                        width = 1.dp,
                                                        color = MaterialTheme.colorScheme.tertiary.copy(alpha = 0.3f),
                                                        shape = MaterialTheme.shapes.small
                                                    )
                                            ) {
                                                Column(
                                                    modifier = Modifier
                                                        .fillMaxSize()
                                                        .padding(8.dp)
                                                        .verticalScroll(rememberScrollState())
                                                ) {
                                                    // Show all warnings in the scrollable container
                                                    importResult!!.warnings.forEach { warning ->
                                                        Text(
                                                            text = "• ${warning.toString()}",
                                                            style = MaterialTheme.typography.bodySmall,
                                                            color = MaterialTheme.colorScheme.tertiary
                                                        )
                                                        Spacer(modifier = Modifier.height(4.dp))
                                                    }
                                                }
                                            }

                                            // Add a small hint text below the scrollable area
                                            if (importResult!!.warnings.size > 5) {
                                                Spacer(modifier = Modifier.height(4.dp))
                                                Text(
                                                    text = "Scroll to see all ${importResult!!.warnings.size} warnings",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                                                    color = MaterialTheme.colorScheme.tertiary.copy(alpha = 0.7f)
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            // Action buttons
                            Button(
                                onClick = {
                                    showImportDetails = false

                                    if (!importResult!!.success) {
                                        // Reset for retry
                                        importResult = null
                                        selectedFileUri = null
                                        selectedFileName = null
                                        showNameField = false
                                    }
                                }
                            ) {
                                Text(if (importResult!!.success) "Done" else "Try Again")
                            }
                        }
                    } else {
                        // Show simple success/failure message
                        Text(
                            text = if (importResult!!.success)
                                "Group data imported successfully!"
                            else
                                "Import failed. See details for more information.",
                            style = MaterialTheme.typography.bodyLarge,
                            color = if (importResult!!.success)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.error
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Summary text
                        Text(
                            text = importResult!!.getSummary(),
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Show details button
                        Button(
                            onClick = { showImportDetails = true }
                        ) {
                            Text("View Details")
                        }
                    }
                } else if (showNameField && selectedFileUri != null) {
                    // File selected, show name field
                    Text(
                        "Selected file: ${selectedFileName ?: "Unknown"}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(16.dp))

                    OutlinedTextField(
                        value = yourName,
                        onValueChange = { yourName = it },
                        label = { Text("Your Name (optional)") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        "If you provide your name, it will be used as the current user in the imported group. " +
                        "Otherwise, the name from the CSV file will be used.",
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = {
                            val uri = selectedFileUri
                            if (uri != null) {
                                scope.launch {
                                    isImporting = true

                                    try {
                                        // Pass the name only if it's not empty
                                        val nameToUse = if (yourName.isBlank()) null else yourName
                                        val result = onImport(uri, nameToUse)
                                        importResult = result
                                    } catch (e: Exception) {
                                        // Create error result if exception occurs
                                        importResult = CsvImportResult(
                                            success = false,
                                            errors = listOf(
                                                CsvImportError(
                                                    message = "Unexpected error: ${e.message}",
                                                    exception = e
                                                )
                                            )
                                        )
                                    } finally {
                                        isImporting = false
                                    }
                                }
                            }
                        }
                    ) {
                        Text("Import Group")
                    }
                } else {
                    // Initial state - show file selection
                    Text(
                        "Import a group from a CSV file. The file should be in the format exported by this app.",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "CSV Format Requirements:\n" +
                        "- First row: Group header (name,members,currentUser)\n" +
                        "- Second row: Group data\n" +
                        "- Third row: Expense header (amount,description,paidBy,splitBetween,category,date)\n" +
                        "- Remaining rows: Expense data\n\n" +
                        "Note: IDs and timestamps are automatically generated during import.",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            openDocumentLauncher.launch(arrayOf("text/csv", "text/comma-separated-values", "*/*"))
                        }
                    ) {
                        Text("Select CSV File")
                    }
                }
            }
        },
        confirmButton = {
            if (importResult != null && importResult!!.success) {
                Button(onClick = onDismiss) {
                    Text("Close")
                }
            }
        },
        dismissButton = {
            if (importResult == null || !importResult!!.success) {
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        }
    )
}
