package com.example.splitexpenses.data.repositories

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.UserFinance
import com.example.splitexpenses.data.source.DataSource
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.util.UUID

class ExpenseRepositoryTest {

    private lateinit var dataSource: DataSource
    private lateinit var groupRepository: GroupRepository
    private lateinit var expenseRepository: ExpenseRepository

    private val testGroupId = "test-group-id"
    private val testGroup = GroupData(
        id = testGroupId,
        name = "Test Group",
        members = listOf("<PERSON>", "<PERSON>", "<PERSON>"),
        expenses = listOf(
            Expense(
                id = "expense1",
                amount = 100.0,
                description = "Dinner",
                paidBy = "Alice",
                splitBetween = listOf("Alice", "<PERSON>", "Charlie"),
                category = "Food",
                date = 1625097600000 // 2021-07-01
            ),
            Expense(
                id = "expense2",
                amount = 60.0,
                description = "Taxi",
                paidBy = "Bob",
                splitBetween = listOf("Alice", "Bob"),
                category = "Transport",
                date = 1625184000000 // 2021-07-02
            )
        ),
        memberUidMap = mapOf("Alice" to "device1", "Bob" to "device2", "Charlie" to "device3"),
        allowedUsers = listOf("device1", "device2", "device3"),
        creatorUid = "device1"
    )

    @Before
    fun setup() {
        dataSource = mockk(relaxed = true)
        groupRepository = mockk(relaxed = true)

        // Mock the currentGroup flow in the groupRepository
        val currentGroupFlow = MutableStateFlow(testGroup)
        every { groupRepository.currentGroup } returns currentGroupFlow

        expenseRepository = ExpenseRepository(dataSource, groupRepository)
    }

    @Test
    fun `addExpense should add expense to current group`() = runTest {
        // Given
        val amount = 50.0
        val description = "Groceries"
        val paidBy = "Charlie"
        val splitBetween = listOf("Alice", "Charlie")
        val category = "Groceries"
        val date = 1625270400000L // 2021-07-03

        // When
        expenseRepository.addExpense(amount, description, paidBy, splitBetween, category, date)

        // Then
        coVerify {
            dataSource.addExpense(
                groupId = testGroupId,
                expense = match { expense ->
                    expense.amount == amount &&
                    expense.description == description &&
                    expense.paidBy == paidBy &&
                    expense.splitBetween == splitBetween &&
                    expense.category == category &&
                    expense.date == date
                }
            )
        }
    }

    @Test
    fun `updateExpense should update existing expense`() = runTest {
        // Given
        val expenseId = "expense1"
        val amount = 120.0
        val description = "Fancy Dinner"
        val paidBy = "Alice"
        val splitBetween = listOf("Alice", "Bob", "Charlie")
        val category = "Food"
        val date = 1625097600000L // 2021-07-01

        // When
        expenseRepository.updateExpense(expenseId, amount, description, paidBy, splitBetween, category, date)

        // Then
        coVerify {
            dataSource.updateExpense(
                groupId = testGroupId,
                expense = match { expense ->
                    expense.id == expenseId &&
                    expense.amount == amount &&
                    expense.description == description &&
                    expense.paidBy == paidBy &&
                    expense.splitBetween == splitBetween &&
                    expense.category == category &&
                    expense.date == date
                }
            )
        }
    }

    @Test
    fun `deleteExpense should delete the specified expense`() = runTest {
        // Given
        val expenseId = "expense1"

        // When
        expenseRepository.deleteExpense(expenseId)

        // Then
        coVerify { dataSource.deleteExpense(testGroupId, expenseId) }
    }

    @Test
    fun `deleteExpenses should delete multiple expenses`() = runTest {
        // Given
        val expenseIds = setOf("expense1", "expense2")

        // When
        expenseRepository.deleteExpenses(expenseIds)

        // Then
        coVerify { dataSource.deleteExpenses(testGroupId, expenseIds) }
    }

    @Test
    fun `calculateFinances should correctly calculate user finances`() {
        // When
        val finances = expenseRepository.calculateFinances()

        // Then
        assertEquals(3, finances.size)

        // Alice paid 100, split 3 ways = 33.33 per person
        // Alice owes herself 33.33, so balance is 100 - 33.33 = 66.67
        val aliceFinance = finances.find { it.userId == "Alice" }
        assertEquals("Alice", aliceFinance?.userId)
        assertEquals(100.0, aliceFinance?.userExpense, 0.01)
        assertEquals(66.67, aliceFinance?.userBalance, 0.01)

        // Bob paid 60, split 2 ways = 30 per person
        // Bob owes Alice 33.33, so balance is 60 - 30 - 33.33 = -3.33
        val bobFinance = finances.find { it.userId == "Bob" }
        assertEquals("Bob", bobFinance?.userId)
        assertEquals(60.0, bobFinance?.userExpense, 0.01)
        assertEquals(-3.33, bobFinance?.userBalance, 0.01)

        // Charlie paid 0, owes Alice 33.33, so balance is -33.33
        val charlieFinance = finances.find { it.userId == "Charlie" }
        assertEquals("Charlie", charlieFinance?.userId)
        assertEquals(0.0, charlieFinance?.userExpense, 0.01)
        assertEquals(-33.33, charlieFinance?.userBalance, 0.01)
    }

    @Test
    fun `calculateFinances should handle payer not in splitBetween`() {
        // Given
        val testGroupWithPayerNotInSplit = GroupData(
            id = testGroupId,
            name = "Test Group",
            members = listOf("Alice", "Bob", "Charlie"),
            expenses = listOf(
                Expense(
                    id = "expense3",
                    amount = 50.0,
                    description = "Gift for Bob",
                    paidBy = "Alice",
                    splitBetween = listOf("Bob"), // Alice paid but is not in splitBetween
                    category = "Gift",
                    date = 1625097600000 // 2021-07-01
                )
            ),
            memberUidMap = mapOf("Alice" to "device1", "Bob" to "device2", "Charlie" to "device3"),
            allowedUsers = listOf("device1", "device2", "device3"),
            creatorUid = "device1"
        )

        // Mock the currentGroup flow with our new test group
        val currentGroupFlow = MutableStateFlow(testGroupWithPayerNotInSplit)
        every { groupRepository.currentGroup } returns currentGroupFlow

        // When
        val finances = expenseRepository.calculateFinances()

        // Then
        assertEquals(3, finances.size)

        // Alice paid 50, not in splitBetween, so balance is 50
        val aliceFinance = finances.find { it.userId == "Alice" }
        assertEquals("Alice", aliceFinance?.userId)
        assertEquals(50.0, aliceFinance?.userExpense, 0.01)
        assertEquals(50.0, aliceFinance?.userBalance, 0.01)

        // Bob didn't pay, but is in splitBetween, so balance is -50
        val bobFinance = finances.find { it.userId == "Bob" }
        assertEquals("Bob", bobFinance?.userId)
        assertEquals(0.0, bobFinance?.userExpense, 0.01)
        assertEquals(-50.0, bobFinance?.userBalance, 0.01)

        // Charlie didn't pay and is not in splitBetween, so balance is 0
        val charlieFinance = finances.find { it.userId == "Charlie" }
        assertEquals("Charlie", charlieFinance?.userId)
        assertEquals(0.0, charlieFinance?.userExpense, 0.01)
        assertEquals(0.0, charlieFinance?.userBalance, 0.01)
    }

    @Test
    fun `calculateFinances should handle empty splitBetween`() {
        // Given
        val testGroupWithEmptySplit = GroupData(
            id = testGroupId,
            name = "Test Group",
            members = listOf("Alice", "Bob", "Charlie"),
            expenses = listOf(
                Expense(
                    id = "expense4",
                    amount = 25.0,
                    description = "Personal expense",
                    paidBy = "Alice",
                    splitBetween = emptyList(), // Empty splitBetween
                    category = "Personal",
                    date = 1625097600000 // 2021-07-01
                )
            ),
            memberUidMap = mapOf("Alice" to "device1", "Bob" to "device2", "Charlie" to "device3"),
            allowedUsers = listOf("device1", "device2", "device3"),
            creatorUid = "device1"
        )

        // Mock the currentGroup flow with our new test group
        val currentGroupFlow = MutableStateFlow(testGroupWithEmptySplit)
        every { groupRepository.currentGroup } returns currentGroupFlow

        // When
        val finances = expenseRepository.calculateFinances()

        // Then
        assertEquals(3, finances.size)

        // Alice paid 25, empty splitBetween, so balance is 0 (paid for herself)
        val aliceFinance = finances.find { it.userId == "Alice" }
        assertEquals("Alice", aliceFinance?.userId)
        assertEquals(25.0, aliceFinance?.userExpense, 0.01)
        assertEquals(0.0, aliceFinance?.userBalance, 0.01)

        // Bob and Charlie didn't pay and are not in splitBetween, so balance is 0
        val bobFinance = finances.find { it.userId == "Bob" }
        assertEquals("Bob", bobFinance?.userId)
        assertEquals(0.0, bobFinance?.userExpense, 0.01)
        assertEquals(0.0, bobFinance?.userBalance, 0.01)

        val charlieFinance = finances.find { it.userId == "Charlie" }
        assertEquals("Charlie", charlieFinance?.userId)
        assertEquals(0.0, charlieFinance?.userExpense, 0.01)
        assertEquals(0.0, charlieFinance?.userBalance, 0.01)
    }

    @Test
    fun `calculateSettlements should correctly calculate optimal settlements`() {
        // When
        val settlements = expenseRepository.calculateSettlements()

        // Then
        assertEquals(2, settlements.size)

        // Charlie owes Alice 33.33
        val charlieToAlice = settlements.find { it.first == "Charlie" && it.second == "Alice" }
        assertEquals("Charlie", charlieToAlice?.first)
        assertEquals("Alice", charlieToAlice?.second)
        assertEquals(33.33, charlieToAlice?.third, 0.01)

        // Bob owes Alice 3.33
        val bobToAlice = settlements.find { it.first == "Bob" && it.second == "Alice" }
        assertEquals("Bob", bobToAlice?.first)
        assertEquals("Alice", bobToAlice?.second)
        assertEquals(3.33, bobToAlice?.third, 0.01)
    }

    @Test
    fun `calculateSettlements should handle payer not in splitBetween`() {
        // Given
        val testGroupWithPayerNotInSplit = GroupData(
            id = testGroupId,
            name = "Test Group",
            members = listOf("Alice", "Bob", "Charlie"),
            expenses = listOf(
                Expense(
                    id = "expense3",
                    amount = 50.0,
                    description = "Gift for Bob",
                    paidBy = "Alice",
                    splitBetween = listOf("Bob"), // Alice paid but is not in splitBetween
                    category = "Gift",
                    date = 1625097600000 // 2021-07-01
                )
            ),
            currentUser = "Alice",
            allowedUsers = listOf("device1", "device2", "device3")
        )

        // Mock the currentGroup flow with our new test group
        val currentGroupFlow = MutableStateFlow(testGroupWithPayerNotInSplit)
        every { groupRepository.currentGroup } returns currentGroupFlow

        // When
        val settlements = expenseRepository.calculateSettlements()

        // Then
        assertEquals(1, settlements.size)

        // Bob owes Alice 50.0
        val bobToAlice = settlements[0]
        assertEquals("Bob", bobToAlice.first)
        assertEquals("Alice", bobToAlice.second)
        assertEquals(50.0, bobToAlice.third, 0.01)
    }

    @Test
    fun `calculateSettlements should handle multiple expenses with different patterns`() {
        // Given
        val testGroupWithMultipleExpenses = GroupData(
            id = testGroupId,
            name = "Test Group",
            members = listOf("Alice", "Bob", "Charlie"),
            expenses = listOf(
                Expense(
                    id = "expense1",
                    amount = 30.0,
                    description = "Lunch",
                    paidBy = "Alice",
                    splitBetween = listOf("Alice", "Bob", "Charlie"),
                    category = "Food",
                    date = 1625097600000 // 2021-07-01
                ),
                Expense(
                    id = "expense2",
                    amount = 45.0,
                    description = "Movie tickets",
                    paidBy = "Bob",
                    splitBetween = listOf("Alice", "Bob", "Charlie"),
                    category = "Entertainment",
                    date = 1625184000000 // 2021-07-02
                ),
                Expense(
                    id = "expense3",
                    amount = 60.0,
                    description = "Gift for Alice",
                    paidBy = "Charlie",
                    splitBetween = listOf("Bob", "Charlie"),
                    category = "Gift",
                    date = 1625270400000 // 2021-07-03
                )
            ),
            memberUidMap = mapOf("Alice" to "device1", "Bob" to "device2", "Charlie" to "device3"),
            allowedUsers = listOf("device1", "device2", "device3"),
            creatorUid = "device1"
        )

        // Mock the currentGroup flow with our new test group
        val currentGroupFlow = MutableStateFlow(testGroupWithMultipleExpenses)
        every { groupRepository.currentGroup } returns currentGroupFlow

        // When
        val finances = expenseRepository.calculateFinances()
        val settlements = expenseRepository.calculateSettlements()

        // Then - verify finances
        assertEquals(3, finances.size)

        // Alice paid 30, split 3 ways = 10 per person
        // Alice balance = 30 - 10 - 15 = 5
        val aliceFinance = finances.find { it.userId == "Alice" }
        assertEquals("Alice", aliceFinance?.userId)
        assertEquals(30.0, aliceFinance?.userExpense, 0.01)
        assertEquals(5.0, aliceFinance?.userBalance, 0.01)

        // Bob paid 45, split 3 ways = 15 per person
        // Bob also owes 10 to Alice and 30 to Charlie
        // Bob balance = 45 - 15 - 10 - 30 = -10
        val bobFinance = finances.find { it.userId == "Bob" }
        assertEquals("Bob", bobFinance?.userId)
        assertEquals(45.0, bobFinance?.userExpense, 0.01)
        assertEquals(-10.0, bobFinance?.userBalance, 0.01)

        // Charlie paid 60, split 2 ways = 30 per person
        // Charlie also owes 10 to Alice and 15 to Bob
        // Charlie balance = 60 - 30 - 10 - 15 = 5
        val charlieFinance = finances.find { it.userId == "Charlie" }
        assertEquals("Charlie", charlieFinance?.userId)
        assertEquals(60.0, charlieFinance?.userExpense, 0.01)
        assertEquals(5.0, charlieFinance?.userBalance, 0.01)

        // Then - verify settlements
        assertEquals(1, settlements.size)

        // Bob owes 10.0 (5.0 to Alice and 5.0 to Charlie)
        val bobSettlement = settlements[0]
        assertEquals("Bob", bobSettlement.first) // Debtor

        // The creditor could be either Alice or Charlie since they both have the same balance
        assertTrue(bobSettlement.second == "Alice" || bobSettlement.second == "Charlie")
        assertEquals(10.0, bobSettlement.third, 0.01)
    }
}
