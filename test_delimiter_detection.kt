// Simple test to verify delimiter detection logic

fun testDelimiterDetection() {
    // Test case 1: Comma-separated with trailing semicolons
    val line1 = "name,members,memberAvatars,categories;;;;;;;;;;;;"
    println("Line 1: $line1")
    
    // Parse with comma
    val commaFields1 = line1.split(",").map { it.trim() }
    val commaFieldsTrimmed1 = commaFields1.dropLastWhile { it.isBlank() }
    val commaNonEmptyCount1 = commaFieldsTrimmed1.count { it.isNotBlank() }
    
    // Parse with semicolon
    val semicolonFields1 = line1.split(";").map { it.trim() }
    val semicolonFieldsTrimmed1 = semicolonFields1.dropLastWhile { it.isBlank() }
    val semicolonNonEmptyCount1 = semicolonFieldsTrimmed1.count { it.isNotBlank() }
    
    println("Comma fields: $commaFieldsTrimmed1 (non-empty: $commaNonEmptyCount1)")
    println("Semicolon fields: $semicolonFieldsTrimmed1 (non-empty: $semicolonNonEmptyCount1)")
    
    val hasTrailingSemicolons1 = line1.endsWith(";;;;") || line1.contains(";;;;")
    println("Has trailing semicolons: $hasTrailingSemicolons1")
    
    val detectedDelimiter1 = when {
        commaNonEmptyCount1 > semicolonNonEmptyCount1 -> ","
        semicolonNonEmptyCount1 > commaNonEmptyCount1 -> ";"
        else -> {
            if (hasTrailingSemicolons1 && commaNonEmptyCount1 > 0) {
                ","
            } else {
                val commaCount = line1.count { it == ',' }
                val semicolonCount = line1.count { it == ';' }
                if (semicolonCount > commaCount) ";" else ","
            }
        }
    }
    
    println("Detected delimiter: '$detectedDelimiter1'")
    println("---")
    
    // Test case 2: Pure semicolon-separated
    val line2 = "name;members;memberAvatars;categories"
    println("Line 2: $line2")
    
    val commaFields2 = line2.split(",").map { it.trim() }
    val commaFieldsTrimmed2 = commaFields2.dropLastWhile { it.isBlank() }
    val commaNonEmptyCount2 = commaFieldsTrimmed2.count { it.isNotBlank() }
    
    val semicolonFields2 = line2.split(";").map { it.trim() }
    val semicolonFieldsTrimmed2 = semicolonFields2.dropLastWhile { it.isBlank() }
    val semicolonNonEmptyCount2 = semicolonFieldsTrimmed2.count { it.isNotBlank() }
    
    println("Comma fields: $commaFieldsTrimmed2 (non-empty: $commaNonEmptyCount2)")
    println("Semicolon fields: $semicolonFieldsTrimmed2 (non-empty: $semicolonNonEmptyCount2)")
    
    val detectedDelimiter2 = when {
        commaNonEmptyCount2 > semicolonNonEmptyCount2 -> ","
        semicolonNonEmptyCount2 > commaNonEmptyCount2 -> ";"
        else -> ","
    }
    
    println("Detected delimiter: '$detectedDelimiter2'")
}

// Expected output:
// Line 1: name,members,memberAvatars,categories;;;;;;;;;;;;
// Comma fields: [name, members, memberAvatars, categories] (non-empty: 4)
// Semicolon fields: [name,members,memberAvatars,categories] (non-empty: 1)
// Has trailing semicolons: true
// Detected delimiter: ','
// ---
// Line 2: name;members;memberAvatars;categories
// Comma fields: [name;members;memberAvatars;categories] (non-empty: 1)
// Semicolon fields: [name, members, memberAvatars, categories] (non-empty: 4)
// Detected delimiter: ';'
