// Test to verify the new delimiter detection logic

fun testNewDelimiterDetection() {
    val expectedGroupFields = listOf("name", "members", "memberAvatars", "categories")

    // Test case 1: Comma-separated with trailing semicolons
    val line1 = "name,members,memberAvatars,categories;;;;;;;;;;;;"
    println("Line 1: $line1")

    // Try comma first
    val commaFields1 = line1.split(",").map { it.trim() }
    val commaFieldsClean1 = commaFields1.dropLastWhile { it.isBlank() }

    // Try semicolon
    val semicolonFields1 = line1.split(";").map { it.trim() }
    val semicolonFieldsClean1 = semicolonFields1.dropLastWhile { it.isBlank() }

    println("Comma fields: $commaFieldsClean1")
    println("Semicolon fields: $semicolonFieldsClean1")

    // Check if comma parsing produces the expected header structure
    val commaMatchesHeader1 = commaFieldsClean1.size >= expectedGroupFields.size &&
            commaFieldsClean1.take(expectedGroupFields.size).zip(expectedGroupFields)
                .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

    // Check if semicolon parsing produces the expected header structure
    val semicolonMatchesHeader1 = semicolonFieldsClean1.size >= expectedGroupFields.size &&
            semicolonFieldsClean1.take(expectedGroupFields.size).zip(expectedGroupFields)
                .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

    println("Comma matches header: $commaMatchesHeader1")
    println("Semicolon matches header: $semicolonMatchesHeader1")

    val detectedDelimiter1 = when {
        commaMatchesHeader1 && !semicolonMatchesHeader1 -> ","
        semicolonMatchesHeader1 && !commaMatchesHeader1 -> ";"
        else -> ","  // Default fallback
    }

    println("Detected delimiter: '$detectedDelimiter1'")
    println("---")

    // Test case 2: Pure semicolon-separated
    val line2 = "name;members;memberAvatars;categories"
    println("Line 2: $line2")

    val commaFields2 = line2.split(",").map { it.trim() }
    val commaFieldsClean2 = commaFields2.dropLastWhile { it.isBlank() }

    val semicolonFields2 = line2.split(";").map { it.trim() }
    val semicolonFieldsClean2 = semicolonFields2.dropLastWhile { it.isBlank() }

    println("Comma fields: $commaFieldsClean2")
    println("Semicolon fields: $semicolonFieldsClean2")

    val commaMatchesHeader2 = commaFieldsClean2.size >= expectedGroupFields.size &&
            commaFieldsClean2.take(expectedGroupFields.size).zip(expectedGroupFields)
                .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

    val semicolonMatchesHeader2 = semicolonFieldsClean2.size >= expectedGroupFields.size &&
            semicolonFieldsClean2.take(expectedGroupFields.size).zip(expectedGroupFields)
                .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

    println("Comma matches header: $commaMatchesHeader2")
    println("Semicolon matches header: $semicolonMatchesHeader2")

    val detectedDelimiter2 = when {
        commaMatchesHeader2 && !semicolonMatchesHeader2 -> ","
        semicolonMatchesHeader2 && !commaMatchesHeader2 -> ";"
        else -> ","  // Default fallback
    }

    println("Detected delimiter: '$detectedDelimiter2'")
}

// Expected output:
// Line 1: name,members,memberAvatars,categories;;;;;;;;;;;;
// Comma fields: [name, members, memberAvatars, categories]
// Semicolon fields: [name,members,memberAvatars,categories]
// Comma matches header: true
// Semicolon matches header: false
// Detected delimiter: ','
// ---
// Line 2: name;members;memberAvatars;categories
// Comma fields: [name;members;memberAvatars;categories]
// Semicolon fields: [name, members, memberAvatars, categories]
// Comma matches header: false
// Semicolon matches header: true
// Detected delimiter: ';'
